#ifndef __COMMON_TYPES_H__
#define __COMMON_TYPES_H__

// Assumes these headers are available
#include <an_pipeline.h>
#include <an_io_handler.h>
#include <an_string.h>
#include <cvector.h>

#ifdef __cplusplus
extern "C" {
#endif

// This struct is shared between the json_parser, workflow, and es_writer plugins
typedef struct {
    data_packet_t header;
    char *type;
    int64_t flow_id;
    char* sip;
    char* dip;
    char *timestamp;
    void *data_pool;
    // --- Fields populated by the workflow plugin ---
    char* sip_geo;
    char* dip_geo;
    void* hs_sens_result;
    an_string_t arena;
    cvector_vector_type(const char*) hit_rules;
} risk_message_t;

#ifdef __cplusplus
}
#endif

#endif /* __COMMON_TYPES_H__ */
