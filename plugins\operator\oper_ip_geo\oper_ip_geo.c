#include <an_calc.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <an_log.h>
#include <IP2Location.h>

#define LOG_TAG "oper_ip_geo"

static const char *const LOGICAL_NAME_IP_INPUT = "ip";
static const char *const LOGICAL_NAME_IP_GEO_OUTPUT = "ip_geo";

typedef enum ip_geo_type_s {
    IP_GEO_UNKNOWN = 0,
    IP_GEO_LOCAL,   // 局域网
    IP_GEO_CHINA,   // 境内
    IP_GEO_ABROAD,  // 境外
} ip_geo_type_t;

/* IP算子结果结构 */
typedef struct {
    ip_geo_type_t geo_type;       /* 是否境外IP */
    char ip_type;                 /* 0,4,6 */
    char country[64];             /* 国家 */
    char city[64];                /* 城市 */
} ip_operator_result_t;

// Plugin's private context. It just needs to store the index of the IP to check.
typedef struct {
    IP2Location *geo_db;
    int ip_index;
    int ip_geo_index;
    ip_operator_result_t result;
} ip_geo_ctx_t;

__thread ip_geo_ctx_t g_ip_ctx = {0}; // 线程本地存储，每个线程都有自己的实例

static const an_operator_variable_req_t ip_geo_dependencies[] = {
    {LOGICAL_NAME_IP_INPUT, AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN} // Terminator
};

static const an_operator_variable_req_t ip_geo_outputs[] = {
    {LOGICAL_NAME_IP_GEO_OUTPUT, AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN} // Terminator
};

static const an_operator_variable_req_t *get_deps()
{
    return ip_geo_dependencies;
}

static const an_operator_variable_req_t *get_outs()
{
    return ip_geo_outputs;
}

// --- Lifecycle and Execution Implementation ---
static int check_ip_geo(char *ip, ip_operator_result_t *result)
{
    /* TODO: 实现IP地理位置查询逻辑 */
    IP2LocationRecord *record = NULL;
    unsigned char in_lan = 0;

    if (!g_ip_ctx.geo_db || !ip) {
        result->geo_type = IP_GEO_UNKNOWN;
        return -1;
    }
    record = IP2Location_get_record(g_ip_ctx.geo_db, (char *)ip, ALL, &in_lan);
    if (record) {
        strcpy(result->country, record->country_long ? record->country_long : "N/A");
        strcpy(result->city, record->city ? record->city : "N/A");
        if (record->in_china == 0) {
            result->geo_type = IP_GEO_ABROAD;
        } else {
            result->geo_type = IP_GEO_CHINA;
        }
        IP2Location_free_record(record);
    } else {
        if (in_lan) {
            result->geo_type = IP_GEO_LOCAL;
        } else {
            result->geo_type = IP_GEO_UNKNOWN;
        }
    }

    return 0;
}

double ip_geo_execute(an_calc_session_t *session, an_calc_oper_ctx_t *oper_ctx)
{
    ip_geo_ctx_t *ctx = (ip_geo_ctx_t *)oper_ctx->user;
    if (!ctx || ctx->ip_index < 0) {
        // 如果没有上下文，说明 init 失败或未被调用
        return 0.0;
    }

    char *ip = an_calc_get_string(session, ctx->ip_index);
    if (!ip) {
        return 0.0;
    }

    // Use real geo check
    //check_ip_geo(ip, &ctx->result);
    ctx->result.geo_type = IP_GEO_ABROAD;
    strcpy(ctx->result.country, "China");
    if (ctx->ip_geo_index >= 0) {
        an_calc_set_string(session, ctx->ip_geo_index, ctx->result.country);
    }
    if (ctx->result.geo_type == IP_GEO_ABROAD) {
        // A real implementation would use a geoip library.
        // For this example, ******* is considered "abroad".
        return 1.0; // HIT
    }

    return 0.0;
}

int ip_geo_init(an_calc_oper_ctx_t *oper_ctx)
{
    LOG_INFO(LOG_TAG, "Initializing op instance for oper_ip_geo");

    // This global init should be handled more robustly in a real app
    // if (!g_ip_ctx.geo_db) {
    //     // In a real app, this path should come from a config file.
    //     char * geo_db_path = "/dlp/usr/local/db/crossbow/IP2LOCATION.BIN_M";
    //     g_ip_ctx.geo_db = IP2Location_open(geo_db_path);
    //     if (g_ip_ctx.geo_db == NULL) {
    //         LOG_ERROR(LOG_TAG, "IP2Location_open failed for path: %s", geo_db_path);
    //         // Non-fatal, execute will just return UNKNOWN
    //     }
    // }

    ip_geo_ctx_t *ctx = (ip_geo_ctx_t *)calloc(1, sizeof(ip_geo_ctx_t));
    if (!ctx) return -1;

    ctx->ip_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_IP_INPUT);
    ctx->ip_geo_index = oper_ctx->get_output_index(oper_ctx, LOGICAL_NAME_IP_GEO_OUTPUT);
    // 检查是否成功获取
    if (ctx->ip_index < 0) {
        LOG_WARN(LOG_TAG, "Framework could not resolve dependency '%s' for this instance.", LOGICAL_NAME_IP_INPUT);
        // 根据业务决定是否要返回失败
    }
    if (ctx->ip_geo_index < 0) {
        LOG_WARN(LOG_TAG, "Framework could not resolve output '%s' for this instance.", LOGICAL_NAME_IP_GEO_OUTPUT);
    }
    oper_ctx->user = ctx;
    LOG_INFO(LOG_TAG, "Dependency 'ip' resolved to index %d, output 'ip_geo' to index %d", ctx->ip_index, ctx->ip_geo_index);
    return 0;
}

void ip_geo_cleanup(an_calc_oper_ctx_t *oper_ctx)
{
    LOG_INFO(LOG_TAG, "Cleaning up op instance: oper_ip_geo");
    // if (g_ip_ctx.geo_db) {
    //     IP2Location_close(g_ip_ctx.geo_db);
    // }

    if (oper_ctx && oper_ctx->user) {
        free(oper_ctx->user);
    }
}

static void ip_geo_reset(an_calc_oper_ctx_t *oper_ctx)
{
    if (oper_ctx && oper_ctx->user) {
        ip_geo_ctx_t *ctx = (ip_geo_ctx_t *)oper_ctx->user;
        // 清空上次计算的结果
        ctx->result.country[0] = '\0';
        ctx->result.city[0] = '\0';
    }
}

an_operator_plugin_t oper_ip_geo = {
    .name = "oper_ip_geo",
    .version = "1.0.0",
    .get_dependencies = get_deps,
    .get_outputs = get_outs,
    .init = ip_geo_init,
    .fini = ip_geo_cleanup,
    .reset = ip_geo_reset,
    .execute = ip_geo_execute
};
