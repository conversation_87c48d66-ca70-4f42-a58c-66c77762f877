include $(DSAP_BUILD_MACROS_MK)

# 依赖头文件
CFLAGS_LOCAL += $(shell pkg-config --cflags libcrossbow libip2lookup)
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/hiredis
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/postgresql/server
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/postgresql/intern
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_util
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_cfg_svc

#$(error $(shell pkg-config --cflags libcommon))

# 依赖库
LDFLAGS += $(shell pkg-config --libs libcrossbow libip2lookup)
LDFLAGS += -L$(INSTALL_ROOT_LIB) -lpcre -lan_util -lan_cfg_svc -lpq -lhiredis -lstdc++ -ldl

# 构建类型
BUILD_TYPE = dynlib

# 构建名称
BUILD_NAME = util_tool

# 版本号
# BUILD_VERSION = 1

# 安装路径
INSTALL_APEEND_PATH =

# 动态库配置
BUILD_DYNLIB_PGKCONFIG = 1

# 头文件
INCLUDE_FILES += khash.h
INCLUDE_FILES += util_ip.h
INCLUDE_FILES += util_tool.h
INCLUDE_FILES += util_log.h
INCLUDE_FILES += util_yaml.h
INCLUDE_FILES += util_pgsql.h
INCLUDE_FILES += util_redis.h

include $(DSAP_BUILD_RULES_MK)
