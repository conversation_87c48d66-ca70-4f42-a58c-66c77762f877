#include "plugin_api.h"
#include "common.h"
#include <an_common.h>
#include <an_cache_file.h>
#include <an_file_loader.h>
#include <an_json.h>
#include <string.h>

#define LOG_TAG "plugin_es_writer"

// --- Context Definition ---
struct es_writer_ctx_s {
    perf_mempool_t* msg_pool;
    cache_file_t** cache_handles;
    file_loader_t* file_loader;
    int thread_count;

    // Configuration (pointers into the config DOM, no need to free)
    const char* dir_path;
    const char* file_prefix;
    const char* es_url;
    const char* index_name;
    size_t buffer_size_thresh;
    size_t line_count_thresh;
    int timeout_ms;
    int loader_threads;
};

// --- Plugin Logic (Lifecycle functions) ---

static int es_writer_process_init(void* context, int thread_num) {
    es_writer_ctx_t* ctx = (es_writer_ctx_t*)context;
    ctx->thread_count = thread_num;
    ctx->cache_handles = calloc(ctx->thread_count, sizeof(cache_file_t*));
    if (!ctx->cache_handles) {
        LOG_ERROR(LOG_TAG, "Failed to allocate memory for cache handles.");
        return PIPELINE_ERROR_MEMORY;
    }

    const char* dirs[] = {ctx->dir_path};
    file_loader_config_t loader_cfg = {
        .dirs_to_scan = (char**)dirs, // Casting const away, file_loader should not modify it.
        .num_dirs = 1,
        .file_pattern = "*.txt",
        .num_worker_threads = ctx->loader_threads,
        .es_url = ctx->es_url,
    };

    if (ctx->loader_threads) {
        ctx->file_loader = file_loader_create_and_start(&loader_cfg);
        if (!ctx->file_loader) {
            LOG_ERROR(LOG_TAG, "Failed to create and start file loader.");
            free(ctx->cache_handles);
            ctx->cache_handles = NULL;
            return PIPELINE_ERROR_PLUGIN_FAILED;
        }
    }

    return PIPELINE_SUCCESS;
}

static void es_writer_process_exit(void* context) {
    es_writer_ctx_t* ctx = (es_writer_ctx_t*)context;
    if (ctx) {
        file_loader_wait_and_destroy(ctx->file_loader);
        free(ctx->cache_handles);
        ctx->cache_handles = NULL;
    }
}

static int es_writer_thread_init(void* context, int thread_idx) {
    es_writer_ctx_t* ctx = (es_writer_ctx_t*)context;
    char prefix[128];
    snprintf(prefix, sizeof(prefix), "%s%d", ctx->file_prefix, thread_idx);

    cache_file_config_t cache_cfg = {
        .dir_path = ctx->dir_path,
        .file_prefix = prefix,
        .buffer_size_thresh = ctx->buffer_size_thresh,
        .line_count_thresh = ctx->line_count_thresh,
        .timeout_ms = ctx->timeout_ms,
    };

    ctx->cache_handles[thread_idx] = cache_file_create(&cache_cfg);
    return ctx->cache_handles[thread_idx] ? PIPELINE_SUCCESS : PIPELINE_ERROR_PLUGIN_FAILED;
}

static void es_writer_thread_exit(void* context, int thread_idx) {
    es_writer_ctx_t* ctx = (es_writer_ctx_t*)context;
    if (ctx && ctx->cache_handles && ctx->cache_handles[thread_idx]) {
        cache_file_destroy(ctx->cache_handles[thread_idx]);
        ctx->cache_handles[thread_idx] = NULL;
    }
}

static int es_writer_thread_idle(void* context, int thread_idx) {
    es_writer_ctx_t* ctx = (es_writer_ctx_t*)context;
    if (ctx->cache_handles[thread_idx]) {
        cache_file_flush_on_timeout(ctx->cache_handles[thread_idx]);
    }
    return PIPELINE_SUCCESS;
}

/**
 * @brief 向JSON对象添加一个字符串字段。
 *        如果val_str为NULL，则不执行任何操作。
 * @param json (yyjson_arena_t *) 指向JSON竞技场句柄。
 * @param obj (yyjson_val *) 指向要添加字段的父JSON对象。
 * @param key (identifier) 字段的键名，同时也是源结构体中的成员名。
 * @param src_ptr (struct pointer) 指向源数据结构体的指针。
 */
#define JSON_ARENA_ADD_STR(obj, key) \
    do { \
        if (msg->key) { \
            yyjson_arena_obj_add_str(&json, obj, #key, msg->key); \
        } \
    } while (0)

/**
 * @brief 向JSON对象添加一个整数（int64_t）字段。
 * @param json (yyjson_arena_t *) 指向JSON竞技场句柄。
 * @param obj (yyjson_val *) 指向要添加字段的父JSON对象。
 * @param key (identifier) 字段的键名，同时也是源结构体中的成员名。
 * @param src_ptr (struct pointer) 指向源数据结构体的指针。
 */
#define JSON_ARENA_ADD_INT(obj, key) \
    yyjson_arena_obj_add_int(&json, obj, #key, msg->key)


/**
 * @brief 向JSON对象添加一个字符串字段，但使用不同的JSON键名。
 * @param json (yyjson_arena_t *) 指向JSON竞技场句柄。
 * @param obj (yyjson_val *) 指向要添加字段的父JSON对象。
 * @param field_name (identifier) 源结构体中的成员名。
 * @param json_key_str (const char *) 在JSON中使用的键名字符串。
 * @param src_ptr (struct pointer) 指向源数据结构体的指针。
 */
#define JSON_ARENA_ADD_STR_NAMED(obj, field_name, json_key_str) \
    do { \
        if (msg->field_name) { \
            yyjson_arena_obj_add_str(&json, obj, json_key_str, msg->field_name); \
        } \
    } while (0)

static int es_writer_thread_process(void* context, int thread_idx, data_packet_t* packet) {
    if (!packet || !packet->data || packet->size != sizeof(risk_message_t)) {
        return PIPELINE_ERROR_INVALID_PARAM;
    }

    es_writer_ctx_t* ctx = (es_writer_ctx_t*)context;
    risk_message_t* msg = (risk_message_t*)packet->data;
    int ret = PIPELINE_SUCCESS;

    // 1. 直接使用 msg->arena 来创建可变JSON对象
    //    所有yyjson相关的内存都将从 msg->arena 分配
    yyjson_arena_t json;
    if (!yyjson_arena_new_obj(&json, &msg->arena)) {
        LOG_ERROR(LOG_TAG, "Failed to create yyjson arena object for ES writer.");
        ret = PIPELINE_ERROR_MEMORY;
        goto cleanup; // 如果创建失败，直接跳转到清理
    }

    yyjson_val *root = yyjson_arena_get_root(&json);

    // 2. 使用宏添加字段
    JSON_ARENA_ADD_STR_NAMED(root, type, "event_type");
    JSON_ARENA_ADD_INT(root, flow_id);
    JSON_ARENA_ADD_STR(root, timestamp);
    JSON_ARENA_ADD_STR(root, sip_geo);
    JSON_ARENA_ADD_STR(root, dip_geo);

    // 3. 将JSON对象序列化为字符串
    //    返回的 json_str 指向的内存也由 msg->arena 管理
    const char* json_str = yyjson_arena_write(&json, 0, NULL);
    if (!json_str) {
        LOG_ERROR(LOG_TAG, "Failed to serialize JSON object for ES writer.");
        ret = PIPELINE_ERROR_PLUGIN_FAILED;
        goto cleanup;
    }

    // 4. 写入文件（业务逻辑）
    cache_file_printf(ctx->cache_handles[thread_idx], "{\"index\":{\"_index\":\"%s\"}}\n%s\n", ctx->index_name, json_str);

cleanup:
    // 5. 统一的资源清理区

    // 释放 msg->arena 会一次性回收所有与 risk_message_t 相关的字符串
    // 以及所有用于创建和序列化JSON的临时内存。
    an_string_release_all(&msg->arena);

    // 释放 cvector 和消息结构体本身
    cvector_free(msg->hit_rules);
    perf_mempool_put(ctx->msg_pool, msg);

    // 标记数据包处理结束
    packet->next = NULL;

    return ret; // 统一的返回点
}



// --- Public API Implementation ---

es_writer_ctx_t* plugin_es_writer_ctx_create(cvector_vector_type(perf_mempool_t*) global_pools, an_config_node_t* params_node)
{
    es_writer_ctx_t* ctx = calloc(1, sizeof(es_writer_ctx_t));
    if (!ctx) return NULL;

    // Find the message pool
    for (size_t i = 0; i < cvector_size(global_pools); ++i) {
        if (strcmp(perf_mempool_get_name(global_pools[i]), "msg_pool") == 0) {
            ctx->msg_pool = global_pools[i];
            break;
        }
    }
    if (!ctx->msg_pool) {
        LOG_ERROR(LOG_TAG, "Required 'msg_pool' not found in global resources.");
        goto fail;
    }

    // Read all parameters from the config node with defaults
    ctx->dir_path = an_config_get_string(an_config_query(params_node, "dir_path"), "/tmp/es_cache");
    ctx->file_prefix = an_config_get_string(an_config_query(params_node, "file_prefix"), "es_data_");
    ctx->index_name = an_config_get_string(an_config_query(params_node, "index_name"), "default_index");
    ctx->es_url = an_config_get_string(an_config_query(params_node, "es_url"), "http://localhost:9200/_bulk");
    ctx->buffer_size_thresh = an_config_get_int(an_config_query(params_node, "buffer_size_thresh"), 1048576); // 1MB
    ctx->line_count_thresh = an_config_get_int(an_config_query(params_node, "line_count_thresh"), 5000);
    ctx->timeout_ms = an_config_get_int(an_config_query(params_node, "timeout_ms"), 30000); // 30s
    ctx->loader_threads = an_config_get_int(an_config_query(params_node, "loader_threads"), 2);

    return ctx;

fail:
    free(ctx);
    return NULL;
}

void plugin_es_writer_ctx_destroy(es_writer_ctx_t* ctx) {
    // No need to free config strings, they are owned by the config DOM.
    // Just free the context itself.
    free(ctx);
}

plugin_t plugin_es_writer_get(es_writer_ctx_t* ctx) {
    return (plugin_t) {
        .name = "es_writer",
        .process_init = es_writer_process_init,
        .process_exit = es_writer_process_exit,
        .thread_init  = es_writer_thread_init,
        .thread_exit  = es_writer_thread_exit,
        .thread_idle  = es_writer_thread_idle,
        .thread_process = es_writer_thread_process,
        .context = ctx,
    };
}
