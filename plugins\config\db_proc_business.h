#ifndef __DB_PROC_BS_H__
#define __DB_PROC_BS_H__

#include "cfg_util.h"
#include "khash.h"
#include "ip2lookup.h"

typedef base_tdata_t business_tdata_t;

// 定义哈希表类型
KHASH_MAP_INIT_STR(domain_hash, sds)

typedef struct {
    sds key;
    sds name;
    sds belong;
    sds network_type;
#ifdef RADIX_IP
    radix_tree_t *ip_tree;
#else
    ip2lookup_t *ip_tree;
#endif
    khash_t(domain_hash) *domains;  // 域名哈希表
} business_item_t;

/**
 * @brief 初始化业务系统配置
 *
 * @param ctx 插件上下文
 * @return int 成功返回0，失败返回-1
 */
int business_config_init();

/**
 * @brief 获取业务系统插件ID
 *
 * @return int 插件ID
 */
int get_business_plugin_id();

/**
 * @brief 释放业务项
 *
 * @param arg 业务项指针
 */
void business_item_destroy(void *arg);

/**
 * @brief 根据域名获取业务项
 *
 * @param domain 要查询的域名
 * @return const business_item_t* 业务项，不存在返回NULL
 */
const business_item_t *get_business_item_by_domain(const char *domain);

/**
 * @brief 根据IP获取业务项
 *
 * @param ip 要查询的IP地址
 * @return const business_item_t* 业务项，不存在返回NULL
 */
const business_item_t *get_business_item_by_ip(const char *ip);

/**
 * @brief 获取业务项中的所有域名
 *
 * @param item 业务项
 * @param domains 用于存储域名的数组
 * @param count 返回域名数量
 * @return int 成功返回0，失败返回-1
 */
int get_all_domains_of_business_item(const business_item_t *item, sds **domains, size_t *count);

#endif // __DB_PROC_BS_H__
