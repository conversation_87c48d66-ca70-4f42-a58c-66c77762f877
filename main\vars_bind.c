#include "cfg_util.h"
#include "vars_bind.h"

#define VARS_BIND_LOG_TAG "vars_bind"

static void hs_check_binding_variable_free(an_operator_binding_t *bindings, size_t binding_count)
{
    // 将 const char * 转换为 char * 以解决编译警告
    free((char *)bindings[1].target_variable);
    free((char *)bindings[2].target_variable);
}

int hs_check_binding_variable(oper_item_t *item, const char *result_var, an_operator_binding_t *bindings, size_t *binding_count)
{
    bindings[*binding_count].logical_name = "hs_check_source";
    bindings[*binding_count].target_variable = get_field_mapping_var(item->fields);
    (*binding_count)++;

    bindings[*binding_count].logical_name = "hs_check_target";
    bindings[*binding_count].target_variable = strdup(item->key);
    (*binding_count)++;

    bindings[*binding_count].logical_name = "hs_check_result";
    bindings[*binding_count].target_variable = strdup(result_var);
    (*binding_count)++;
}

void binding_variable_free(const char *plugin_name, an_operator_binding_t *bindings, size_t binding_count)
{
    if (plugin_name == NULL) {
        return;
    }

    if (strcmp(plugin_name, "hs_check") == 0) {
        hs_check_binding_variable_free(bindings, binding_count);
    }
}
