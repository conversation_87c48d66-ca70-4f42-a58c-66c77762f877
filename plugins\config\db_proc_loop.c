#include "cfg_util.h"
#include "db_proc_loop.h"

static int loop_plugin_id = -1;

static int loop_tdata_destroy(const void *arg)
{
    if (!arg) {
        return CFG_SUCCESS;
    }

    loop_tdata_t *tdata = (loop_tdata_t *)arg;

    // 释放向量（会自动调用loop_item_destroy释放每个item）
    cvector_free(tdata->items);

    free(tdata);
    return CFG_SUCCESS;
}

static void loop_item_destroy(void *arg)
{
    if (!arg) {
        return;
    }

    loop_item_t *item = (loop_item_t *)arg;

    if (item->key) {
        sdsfree(item->key);
        item->key = NULL;
    }

    if (item->name) {
        sdsfree(item->name);
        item->name = NULL;
    }

    if (item->expr) {
        sdsfree(item->expr);
        item->expr = NULL;
    }

    if (item->level) {
        sdsfree(item->level);
        item->level = NULL;
    }

    item->type = -1;
    item->state = -1;

    free(item);
}

static int loop_item_create(items_t *items, PGresult *res, size_t row)
{
    loop_item_t *item = (loop_item_t *)calloc(1, sizeof(loop_item_t));
    if (!item) {
        return CFG_FAILURE;
    }

    item->key = sdsnew(safe_get_value(res, row, 0));
    item->name = sdsnew(safe_get_value(res, row, 1));
    item->expr = sdsnew(safe_get_value(res, row, 2));
    item->level = sdsnew(safe_get_value(res, row, 3));
    item->type = cfg_str_to_int(safe_get_value(res, row, 4));
    item->state = cfg_str_to_int(safe_get_value(res, row, 5));

    if (!item->key || !item->name || !item->expr || !item->level) {
        loop_item_destroy(item);
        return CFG_FAILURE;
    }

    cvector_push_back(*items, item);

    return CFG_SUCCESS;
}

static const cfg_module_create_t module_desc = {
    .expect_cols = API_LOOP_COL_NUM,
    .table_name = API_LOOP_TABLE,
    .redis_key = API_REDIS_KEY_LOOP,
    .tdata_size = sizeof(loop_tdata_t),
    .item_size = sizeof(loop_item_t),
    .create_item = loop_item_create,
    .destroy_item = loop_item_destroy,
    .post_process = NULL,
    .destroy_tdata = loop_tdata_destroy,
};

static void *loop_tdata_create(void *arg)
{
    return cfg_module_create((void *)&module_desc);
}

static config_plugin_t loop_plugin = {
    .name = "loop",
    .check_interval = 1,
    .grace_period = 3,
    .create = loop_tdata_create,
    .destroy = loop_tdata_destroy,
};

int loop_config_init()
{
    loop_plugin_id = an_cfg_plugin_register(&loop_plugin);
    return loop_plugin_id;
}

int get_loop_plugin_id()
{
    return loop_plugin_id;
}
