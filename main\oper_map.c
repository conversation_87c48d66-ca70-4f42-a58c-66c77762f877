#include "oper_map.h"
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <pcre.h>
#include <sds.h>
#include "util_log.h"

#define OPER_MAP_LOG_TAG "oper_map"

static khash_t(operator_map) *g_oper_map = NULL;

static void oper_map_free(oper_map_t *mapping)
{
    if (!mapping) return;

    sdsfree(mapping->operator_id);
    sdsfree(mapping->operator_name);
    sdsfree(mapping->operator_type);
    sdsfree(mapping->input_vars);
    sdsfree(mapping->output_vars);

    free(mapping);
}

void destroy_operator_map(void)
{
    if (!g_oper_map) return;

    khiter_t k;
    for (k = kh_begin(g_oper_map); k != kh_end(g_oper_map); ++k) {
        if (kh_exist(g_oper_map, k)) {
            oper_map_free(kh_value(g_oper_map, k));
        }
    }

    kh_destroy(operator_map, g_oper_map);
    g_oper_map = NULL;
}

int init_operator_map(void)
{
    if (g_oper_map) {
        destroy_operator_map();
    }

    g_oper_map = kh_init(operator_map);

    if (!g_oper_map) {
        return -1;
    }

    return 0;
}

int add_operator_map(oper_map_t *oper_map)
{
    if (!g_oper_map || !oper_map) {
        return -1;
    }

    int ret;
    khiter_t k = kh_put(operator_map, g_oper_map, oper_map->operator_id, &ret);
    if (ret == -1) {
        oper_map_free(oper_map);
        return -1;
    }

    // 如果已存在，先释放旧的
    if (ret == 0) {
        oper_map_free(kh_value(g_oper_map, k));
    }

    kh_value(g_oper_map, k) = oper_map;

    return 0;
}

const char *get_operator_name_by_id(const char *oper_id)
{
    if (!g_oper_map || !oper_id) {
        return NULL;
    }

    khiter_t k = kh_get(operator_map, g_oper_map, oper_id);
    if (k == kh_end(g_oper_map)) {
        return NULL;
    }

    oper_map_t *mapping = kh_value(g_oper_map, k);
    return mapping ? mapping->operator_name : NULL;
}

oper_map_t *get_operator_item_by_id(const char *operator_id)
{
    if (!g_oper_map || !operator_id) {
        return NULL;
    }

    khiter_t k = kh_get(operator_map, g_oper_map, operator_id);
    if (k == kh_end(g_oper_map)) {
        return NULL;
    }

    return kh_value(g_oper_map, k);
}

/**
 * @brief 检查字符是否为标识符字符
 */
static inline bool is_identifier_char(char c)
{
    return isalnum(c) || c == '_';
}

/**
 * @brief 检查字符是否为分隔符
 */
static inline bool is_separator_char(char c)
{
    return c == ' ' || c == '\t' || c == '\n' || c == '\r' ||
        c == '(' || c == ')' || c == '|' || c == '&' ||
        c == '!' || c == '=' || c == '<' || c == '>' ||
        c == '+' || c == '-' || c == '*' || c == '/' ||
        c == ',' || c == ';' || c == ':';
}

/**
 * @brief 在表达式中查找完整的标识符匹配
 * @param expr 表达式
 * @param target 目标标识符
 * @param start_pos 开始搜索位置
 * @return const char* 找到的位置，未找到返回NULL
 */
static const char *find_complete_identifier(const char *expr, const char *target, const char *start_pos)
{
    if (!expr || !target || !start_pos) {
        return NULL;
    }

    size_t target_len = strlen(target);
    const char *pos = start_pos;

    while (*pos) {
        // 查找目标字符串
        const char *found = strstr(pos, target);
        if (!found) {
            return NULL;
        }

        // 检查是否为完整标识符
        bool is_complete = true;

        // 检查前一个字符
        if (found > expr) {
            char prev_char = *(found - 1);
            if (is_identifier_char(prev_char)) {
                is_complete = false;
            }
        }

        // 检查后一个字符
        if (is_complete) {
            char next_char = *(found + target_len);
            if (next_char != '\0' && is_identifier_char(next_char)) {
                is_complete = false;
            }
        }

        if (is_complete) {
            return found;
        }

        // 继续搜索
        pos = found + 1;
    }

    return NULL;
}

/**
 * @brief 替换逻辑运算符
 * @param expr 表达式
 * @return sds 替换后的表达式，需要调用者释放
 */
static sds replace_logical_operators(const char *expr)
{
    if (!expr) {
        return NULL;
    }

    sds result = sdsnew(expr);
    if (!result) {
        return NULL;
    }

    // 替换逻辑运算符
    sds temp = sdsnew("");
    if (!temp) {
        sdsfree(result);
        return NULL;
    }

    const char *pos = result;
    while (*pos) {
        if (*pos == '|' && *(pos + 1) != '|') {
            // 单个 | 替换为 or
            temp = sdscat(temp, " or ");
            pos++;
        } else if (*pos == '&' && *(pos + 1) != '&') {
            // 单个 & 替换为 and
            temp = sdscat(temp, " and ");
            pos++;
        } else if (*pos == '!' && *(pos + 1) != '=') {
            // ! 替换为 not（避免 != 的情况）
            temp = sdscat(temp, "not ");
            pos++;
        } else {
            // 其他字符直接添加
            temp = sdscatlen(temp, pos, 1);
            pos++;
        }

        if (!temp) {
            sdsfree(result);
            return NULL;
        }
    }

    sdsfree(result);
    return temp;
}

/**
 * @brief 转换表达式中的算子ID为输出变量名
 * @param expr 原始表达式
 * @return sds 转换后的表达式，需要调用者释放
 */
sds convert_expr_operator(const char *expr)
{
    if (!expr || !g_oper_map) {
        return NULL;
    }

    sds result = sdsnew(expr);
    if (!result) {
        LOG_ERROR(OPER_MAP_LOG_TAG, "Failed to allocate memory for expression conversion");
        return NULL;
    }

    // 遍历所有算子映射，进行替换
    khiter_t k;
    for (k = kh_begin(g_oper_map); k != kh_end(g_oper_map); ++k) {
        if (!kh_exist(g_oper_map, k)) {
            continue;
        }

        oper_map_t *mapping = kh_value(g_oper_map, k);
        if (!mapping || !mapping->operator_id || !mapping->output_vars) {
            continue;
        }

        // 使用精确匹配查找算子ID
        const char *pos = result;
        const char *found;
        sds temp_result = sdsnew("");
        if (!temp_result) {
            sdsfree(result);
            return NULL;
        }

        size_t operator_id_len = strlen(mapping->operator_id);
        bool has_replacement = false;

        while ((found = find_complete_identifier(result, mapping->operator_id, pos)) != NULL) {
            has_replacement = true;

            // 添加匹配位置之前的内容
            temp_result = sdscatlen(temp_result, pos, found - pos);
            if (!temp_result) {
                sdsfree(result);
                return NULL;
            }

            // 添加替换的输出变量名
            temp_result = sdscat(temp_result, mapping->output_vars);
            if (!temp_result) {
                sdsfree(result);
                return NULL;
            }

            pos = found + operator_id_len;

            LOG_DEBUG(OPER_MAP_LOG_TAG, "Replaced operator '%s' with '%s'", mapping->operator_id, mapping->output_vars);
        }

        // 如果有替换，添加剩余内容并更新结果
        if (has_replacement) {
            temp_result = sdscat(temp_result, pos);
            if (!temp_result) {
                sdsfree(result);
                return NULL;
            }

            sdsfree(result);
            result = temp_result;
        } else {
            sdsfree(temp_result);
        }
    }

    // 替换逻辑运算符
    sds final_result = replace_logical_operators(result);
    sdsfree(result);

    if (!final_result) {
        LOG_ERROR(OPER_MAP_LOG_TAG, "Failed to replace logical operators");
        return NULL;
    }

    return final_result;
}

/*
 * @brief 遍历哈希表里的所有数据，并打印
 * @param void
 * @return void
*/
void print_operator_map(void)
{
    if (!g_oper_map) {
        return;
    }

    khiter_t k;
    for (k = kh_begin(g_oper_map); k != kh_end(g_oper_map); ++k) {
        if (kh_exist(g_oper_map, k)) {
            oper_map_t *mapping = kh_value(g_oper_map, k);
            LOG_INFO(OPER_MAP_LOG_TAG, "oper_id: %s, oper_name: %s, oper_type: %s, input_vars: %s, output_vars: %s", mapping->operator_id,
                    mapping->operator_name, mapping->operator_type, mapping->input_vars, mapping->output_vars);
        }
    }
}
