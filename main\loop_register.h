#ifndef __LOOP_REGISTER_H__
#define __LOOP_REGISTER_H__

#include "plugin_api.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 动态注册所有变量
 * @param ctx 工作流上下文
 * @return int 成功返回0，失败返回-1
 */
int dynamic_register_vars(workflow_ctx_t *ctx);

/**
 * @brief 动态注册所有算子
 * @param ctx 工作流上下文
 * @return int 成功返回0，失败返回-1
 */
int dynamic_register_opers(workflow_ctx_t *ctx);

/**
 * @brief 动态注册所有规则
 * @param ctx 工作流上下文
 * @return int 成功返回0，失败返回-1
 */
int dynamic_register_loops(workflow_ctx_t *ctx);

/**
 * @brief 动态缓存变量索引
 * @param ctx 工作流上下文
 */
void dynamic_cache_variables_index(workflow_ctx_t *ctx);

#ifdef __cplusplus
}
#endif

#endif // __LOOP_REGISTER_H__


