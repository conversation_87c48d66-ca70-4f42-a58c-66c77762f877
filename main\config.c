#include "analyser.h"
#include <an_config.h>

an_config_node_t *g_conf_root;

an_config_node_t *get_app_conf()
{
    return g_conf_root;
}

bool load_config(const char *config_path)
{
    // 1. 加载配置
    LOG_INFO(LOG_TAG, "Loading configuration from '%s'", config_path);
    g_conf_root = an_config_load(config_path);
    if (!g_conf_root)
    {
        return false;
    }

    return true;
}

void free_config()
{
    an_config_free(g_conf_root);
}
