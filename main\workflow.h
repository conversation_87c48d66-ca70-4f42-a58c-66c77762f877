#ifndef __WORKFLOW_H__
#define __WORKFLOW_H__

#include <cvector.h>
#include <an_calc.h>
#include <an_pipeline.h>
#include <an_io_handler.h>
#include <an_string.h>

#ifdef __cplusplus
extern "C" {
#endif

    struct workflow_ctx_s {
        an_calc_t *engine;
        an_calc_session_t **sessions;
        int thread_count;

        int sip_idx;
        int dip_idx;
        int url_idx;
        int body_idx;
        int domain_idx;
        int header_idx;
        int cookies_idx;

        int bs_idx;
        int sip_geo_idx;
        int dip_geo_idx;
        int hs_url_result_idx;
        int hs_body_result_idx;
        int hs_cookies_result_idx;
        int hs_header_result_idx;
    };

    /**
     * @brief 初始化工作流，包括加载规则引擎和配置。
     *
     * @param plugin_path 算子插件(.so)所在的目录路径。
     * @return int 成功返回0，失败返回-1。
     */
    int workflow_init(const char *plugin_path);

    /**
     * @brief 销毁工作流，释放所有相关资源。
     */
    void workflow_destroy(void);

    /**
     * @brief 获取用于集成到Pipeline的消息处理插件。
     *
     * @return plugin_t 返回一个配置好的插件，可以直接注册到pipeline中。
     */
    plugin_t workflow_get_plugin(void);

#ifdef __cplusplus
}
#endif

#endif /* __WORKFLOW_H__ */
