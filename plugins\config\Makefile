include $(DSAP_BUILD_MACROS_MK)

# 依赖头文件
CFLAGS_LOCAL += $(shell pkg-config --cflags libcrossbow libip2lookup)
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_util
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/util_tool
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_cfg_svc

#$(error $(shell pkg-config --cflags libcommon))

# 依赖库
LDFLAGS += $(shell pkg-config --libs libcrossbow libip2lookup)
LDFLAGS += -L$(INSTALL_ROOT_LIB) -lpcre -lan_util -lan_cfg_svc -lutil_tool -lstdc++ -ldl

# 构建类型
BUILD_TYPE = dynlib

# 构建名称
BUILD_NAME = cfg_svc

# 版本号
# BUILD_VERSION = 1

# 安装路径
INSTALL_APEEND_PATH =

# 动态库配置
BUILD_DYNLIB_PGKCONFIG = 1

# 头文件
INCLUDE_FILES += cfg_init.h
INCLUDE_FILES += cfg_util.h
INCLUDE_FILES += hs_match.h
INCLUDE_FILES += hs_create.h
INCLUDE_FILES += db_proc_oper.h
INCLUDE_FILES += db_proc_dict.h
INCLUDE_FILES += db_proc_regu.h
INCLUDE_FILES += db_proc_loop.h
INCLUDE_FILES += db_proc_snort.h
INCLUDE_FILES += db_proc_white.h
INCLUDE_FILES += db_proc_business.h

include $(DSAP_BUILD_RULES_MK)
