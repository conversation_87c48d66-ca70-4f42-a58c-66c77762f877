// yaml_config_parser.h

#ifndef __UTIL_YAML_H__
#define __UTIL_YAML_H__

#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define YAML_DEFAULT_S(v) {.s = v}
#define YAML_DEFAULT_I(v) {.i = v}
#define YAML_DEFAULT_L(v) {.l = v}
#define YAML_DEFAULT_D(v) {.d = v}
#define YAML_DEFAULT_B(v) {.b = v}
#define YAML_DEFAULT_NONE {.s = NULL}

    typedef enum {
        YAML_TYPE_STRING,
        YAML_TYPE_INT,
        YAML_TYPE_LONG,
        YAML_TYPE_DOUBLE,
        YAML_TYPE_BOOL
    } yaml_field_type_t;

    typedef union {
        const char *s; // String
        int         i; // Integer
        long        l; // Long
        double      d; // Double
        bool        b; // Boolean
    } yaml_default_value_t;

    typedef struct {
        const char *yaml_path;
        const char *description;
        yaml_field_type_t type;
        yaml_default_value_t default_value;

        size_t offset;
        size_t size;
    } cb_config_map_t;

    /**
     * @brief 打印配置帮助（包含当前值）
     *
     * @param map 字段映射表
     * @param user_struct 用户结构体
     */
    void yaml_config_print_help(const cb_config_map_t *map, const void *user_struct);

    /**
     * @brief 解析YAML配置文件
     *
     * @param filepath 配置文件路径
     * @param map 字段映射表
     * @param user_struct 用户结构体
     */
    int yaml_config_parse(const char *filepath, const cb_config_map_t *map, void *user_struct);

#ifdef __cplusplus
}
#endif

#endif // __UTIL_YAML_H__