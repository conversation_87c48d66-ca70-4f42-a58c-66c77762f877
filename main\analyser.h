#ifndef __ANALYSER_H__
#define __ANALYSER_H__

#include <an_common.h>
enum RunModes {
    RUNMODE_NONE = 0,
    RUNMODE_UNKNOWN,
    RUNMODE_RUNNING,
    RUNMODE_UNITEST,
    RUNMO<PERSON>_CMDLINE,
    RUNMODE_LIST_UNITTEST,
    RUNMODE_CONFTEST,
};

enum RunModes run_mode;

typedef struct ANInstance {
    enum RunModes run_mode;
    const char *name;
    int daemon;
    char *regex_arg;
    char *config_file;
    char *config_log;
}ANInstance;
ANInstance *get_app_instance();
#define LOG_TAG "risk"
#endif
