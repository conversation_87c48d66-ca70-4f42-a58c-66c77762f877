#include "util_pgsql.h"
#include <stdio.h>
#include <stdlib.h>

/**
 * @brief 连接数据库
 *
 * @param conn_str 数据库连接字符串
 * @return 数据库连接
 */
PGconn *pg_connect(const char *conn_str)
{
    PGconn *conn = PQconnectdb(conn_str);

    // 检查连接状态
    if (PQstatus(conn) != CONNECTION_OK) {
        fprintf(stderr, "PostgreSQL connection failed: %s", PQerrorMessage(conn));
        PQfinish(conn); // 即使连接失败，也应该调用 PQfinish 清理
        return NULL;
    }

    // 推荐设置一个安全的 search_path
    // 这可以防止特权用户创建的函数被其他用户以不可预见的方式调用
    // PGresult *res = PQexec(conn, "SELECT pg_catalog.set_config('search_path', '', false)");
    // if (PQresultStatus(res) != PGRES_TUPLES_OK) {
    //     fprintf(stderr, "Failed to set search_path: %s", PQerrorMessage(conn));
    //     PQclear(res);
    //     PQfinish(conn);
    //     return NULL;
    // }
    // PQclear(res);

    return conn;
}

/**
 * @brief 断开连接
 *
 * @param conn 数据库连接
 */
void pg_close(PGconn *conn)
{
    if (conn) {
        PQfinish(conn);
    }
}

/**
 * @brief 执行非查询类命令 (INSERT, UPDATE, DELETE, etc.)
 *
 * @param conn 数据库连接
 * @param command 命令
 * @param nParams 参数数量
 * @param paramValues 参数值
 * @param callback 回调函数
 * @param userData 用户数据
 * @return 执行结果
 */
int pg_execute_command(PGconn *conn, const char *command, int nParams, const char *const *paramValues,
                         PGResultCallback callback, void *userData)
{
    if (!conn) {
        fprintf(stderr, "Invalid PostgreSQL connection object provided.");
        return -1;
    }

    PGresult *res = PQexecParams(conn, command, nParams, NULL, paramValues, NULL, NULL, 0);

    ExecStatusType status = PQresultStatus(res);
    if (status != PGRES_COMMAND_OK) {
        fprintf(stderr, "PostgreSQL command failed: %s", PQresultErrorMessage(res));
        PQclear(res);
        return -1;
    }

    // 命令成功，如果提供了回调，则调用它
    int callback_ret = 0;
    if (callback) {
        callback_ret = callback(res, userData);
    }

    PQclear(res); // 无论如何都要释放结果
    return callback_ret; // 返回回调的执行结果
}

/**
 * @brief 执行查询 (SELECT)
 *
 * @param conn 数据库连接
 * @param query 查询语句
 * @param nParams 参数数量
 * @param paramValues 参数值
 * @param callback 回调函数
 * @param userData 用户数据
 * @return 查询结果
 */
int pg_execute_query(PGconn *conn, const char *query, int nParams, const char *const *paramValues,
                       PGResultCallback callback, void *userData)
{
    // 对于查询，回调是必须的，否则结果就丢失了
    if (!callback) {
        fprintf(stderr, "A callback function must be provided for pg_execute_query.");
        return -1;
    }
    if (!conn) {
        fprintf(stderr, "Invalid PostgreSQL connection object provided.");
        return -1;
    }

    PGresult *res = PQexecParams(conn, query, nParams, NULL, paramValues, NULL, NULL, 0);
    if (PQresultStatus(res) != PGRES_TUPLES_OK) {
        fprintf(stderr, "PostgreSQL query failed: %s", PQresultErrorMessage(res));
        PQclear(res);
        return -1;
    }

    // 查询成功，调用回调函数处理结果
    int callback_ret = callback(res, userData);

    PQclear(res); // 处理完毕，释放结果集

    return callback_ret;
}

/**
 * @brief 打印结果集
 * @param res 结果集
 */
void pg_print_result(const PGresult *res)
{
    if (!res) {
        return;
    }

    int nFields = PQnfields(res);
    int nRows = PQntuples(res);

    // 打印表头
    for (int i = 0; i < nFields; i++) {
        printf("%-15s", PQfname(res, i));
    }
    printf("\n\n");

    // 打印每一行
    for (int i = 0; i < nRows; i++) {
        for (int j = 0; j < nFields; j++) {
            printf("%-15s", PQgetvalue(res, i, j));
        }
        printf("\n");
    }
}
