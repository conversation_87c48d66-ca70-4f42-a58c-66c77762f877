#include "cfg_util.h"
#include "db_proc_regu.h"
#include "db_proc_dict.h"
#include "db_proc_loop.h"
#include "db_proc_oper.h"
#include "db_proc_snort.h"
#include "db_proc_white.h"
#include "hs_create.h"
#include "db_proc_business.h"
#include "cfg_init.h"

static const cb_config_map_t map[] = {
    {
        "cfg.redis.host",
        "redis host",
        YAML_TYPE_STRING,
        YAML_DEFAULT_S("127.0.0.1"),
        offsetof(cfg_conf_t, redis_host),
        sizeof(((cfg_conf_t *)0)->redis_host)
    },
    {
        "cfg.redis.port",
        "redis port",
        YAML_TYPE_INT,
        YAML_DEFAULT_I(6379),
        offsetof(cfg_conf_t, redis_port),
        0
    },
    {
        "cfg.redis.db",
         "redis db",
         YAM<PERSON>_TYPE_INT,
         YAML_DEFAULT_I(12),
         offsetof(cfg_conf_t, redis_db),
         0
    },
    {
        "cfg.redis.key",
        "redis key",
        YAML_TYPE_STRING,
        YAML_DEFAULT_S("policy"),
        offsetof(cfg_conf_t, redis_key),
        sizeof(((cfg_conf_t *)0)->redis_key)
    },
    {
        "cfg.http.host",
        "http host",
        YAML_TYPE_STRING,
        YAML_DEFAULT_S("127.0.0.1"),
        offsetof(cfg_conf_t, http_host),
        sizeof(((cfg_conf_t *)0)->http_host)
    },
    {
        "cfg.http.port",
        "http port",
        YAML_TYPE_INT,
        YAML_DEFAULT_I(8080),
        offsetof(cfg_conf_t, http_port),
        0
    },
    {
        "cfg.pgsql.conn",
        "pgsql conn",
        YAML_TYPE_STRING,
        YAML_DEFAULT_S("user=dsaper password=dsaper host=127.0.0.1 port=5432 dbname=dsapdb connect_timeout=1 options='-c search_path=api,public'"),
        offsetof(cfg_conf_t, pg_conn),
        sizeof(((cfg_conf_t *)0)->pg_conn)
    },
    {
        "cfg.mode",
        "mode",
        YAML_TYPE_INT,
        YAML_DEFAULT_I(1),
        offsetof(cfg_conf_t, mode),
        0
    },
    {
        "cfg.check_cycle",
        "check cycle",
        YAML_TYPE_INT,
        YAML_DEFAULT_I(3),
        offsetof(cfg_conf_t, check_cycle),
        0
    },
    {
        "cfg.snort.file",
        "snort file",
        YAML_TYPE_STRING,
        YAML_DEFAULT_S("snort.rules"),
        offsetof(cfg_conf_t, snort_file),
        sizeof(((cfg_conf_t *)0)->snort_file)
    },
    {
        "cfg.snort.reload",
        "snort reload cmd",
        YAML_TYPE_STRING,
        YAML_DEFAULT_S("/usr/local/bin/snort -R /etc/snort/rules/snort.rules"),
        offsetof(cfg_conf_t, snort_reload_cmd),
        sizeof(((cfg_conf_t *)0)->snort_reload_cmd)
    },
    {NULL, NULL, (yaml_field_type_t)0, {0}, 0, 0} // 哨兵元素
};

void *cfg_init(cfg_ctx_t *ctx)
{
    if (!ctx) {
        return NULL;
    }

    // 2. 分配并解析配置
    g_conf = calloc(1, sizeof(cfg_conf_t));
    if (!g_conf) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to allocate memory for global config");
        return NULL;
    }

    if (yaml_config_parse(ctx->cfg_yaml_path, map, g_conf) != CFG_SUCCESS) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to parse YAML config: %s", ctx->cfg_yaml_path);
        free(g_conf);
        return NULL;
    }

    regu_config_init(ctx->thread_num);
    dict_config_init();
    oper_config_init();
    loop_config_init();
    white_config_init();
    business_config_init();

    return NULL;
}

void test_cfg(void)
{
    while (1) {
        const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(get_business_plugin_id());
        if (!cfg_wrap || !cfg_wrap->config) {
            LOG_ERROR(CFG_LOG_TAG, "Failed to get config wrapper for business plugin");
            sleep(1);
            continue;
        }

        business_tdata_t *business_tdata = (business_tdata_t *)cfg_wrap->config;

        // 遍历所有业务项
        for (size_t i = 0; i < cvector_size(business_tdata->items); i++) {
            business_item_t *item = (business_item_t *)business_tdata->items[i];
            if (!item || !item->domains) {
                continue;
            }

            // 遍历该业务项的所有域名
            khiter_t k;
            for (k = kh_begin(item->domains); k != kh_end(item->domains); ++k) {
                if (!kh_exist(item->domains, k)) {
                    continue;
                }
                const char *domain = kh_key(item->domains, k);
                LOG_INFO(CFG_LOG_TAG, "  - Domain: %s", domain);
            }
        }

        // 测试域名匹配
        const char *test_domains[] = {
            "system_domains_2",
            "system_domains_3",
            "system_domains_4",
            "system_domains_5",
            "non_existent_domain"
        };

        for (size_t i = 0; i < sizeof(test_domains) / sizeof(test_domains[0]); i++) {
            const business_item_t *item = get_business_item_by_domain(test_domains[i]);
            if (item) {
                LOG_INFO(CFG_LOG_TAG, "Found domain '%s' in business: key=%s, name=%s, belong=%s",
                        test_domains[i], item->key, item->name, item->belong);
            } else {
                LOG_INFO(CFG_LOG_TAG, "Domain '%s' not found in any business", test_domains[i]);
            }
        }

        // 测试获取所有域名
        for (size_t i = 0; i < cvector_size(business_tdata->items); i++) {
            business_item_t *item = (business_item_t *)business_tdata->items[i];
            if (!item || !item->domains) {
                continue;
            }

            sds *domains = NULL;
            size_t domain_count = 0;
            if (get_all_domains_of_business_item(item, &domains, &domain_count) == CFG_SUCCESS) {
                LOG_INFO(CFG_LOG_TAG, "Business '%s' has %zu domains:", item->name, domain_count);
                for (size_t j = 0; j < domain_count; j++) {
                    LOG_INFO(CFG_LOG_TAG, "  - %s", domains[j]);
                    sdsfree(domains[j]);
                }
                free(domains);
            }
        }

        sleep(1);
    }
}
