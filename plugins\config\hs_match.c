#include "hs_match.h"

// 静态内联函数，用于安全地取两个size_t值的较小者
static inline size_t min_size(size_t a, size_t b)
{
    return a < b ? a : b;
}

// 静态内联函数，用于安全地取两个uint64_t值的较大/较小者
static inline uint64_t max_u64(uint64_t a, uint64_t b)
{
    return a > b ? a : b;
}

static inline uint64_t min_u64(uint64_t a, uint64_t b)
{
    return a < b ? a : b;
}

/**
 * @brief 将规则键字符串转换为数值ID
 * @param key 规则键字符串
 * @param id 输出参数，转换后的ID
 * @return 成功返回 true，失败返回 false
 */
static bool hs_convert_rule_key(const char *key, uint64_t *id)
{
    if (!key || !id) {
        return false;
    }

    errno = 0;
    char *endptr = NULL;
    *id = strtoull(key, &endptr, 10);

    if (errno != 0 || *endptr != '\0' || endptr == key) {
        return false;
    }

    return true;
}

static void hs_feature_extract(match_item_t *item)
{
    (void)item;
}

/**
 * @brief 安全地更新匹配项内容，并添加 null 终止符
 *
 * @param item 指向要更新的 match_item_t。
 * @param data 指向包含匹配内容的原始数据缓冲区。
 * @param from 匹配内容在 data 中的起始偏移量。
 * @param to 匹配内容在 data 中的结束偏移量（不包含）。
 */
static bool update_item_content(match_item_t *item, const char *source, unsigned long long from, unsigned long long to)
{
    size_t new_length = to - from + 2; // +2 for comma and null terminator

    if (new_length < 0) {
        return false;
    }

    char *new_content = (char *)calloc(1, new_length);
    if (!new_content) {
        return false;
    }

    memcpy(new_content, source + from, to - from);
    new_content[to - from] = '\0';

    if (item->matched) {
        free(item->matched);
        item->matched = NULL;
    }

    item->matched = new_content;
    item->matched_len = to - from;

    // hs_feature_extract(item); // 如果需要，调用特征提取

    return true;
}

/**
 * @brief 向敏感项追加新的匹配内容
 * TODO: 需要优化，如果匹配内容过长，需要分段处理
 * @param item 要更新的敏感项
 * @param data 原始数据缓冲区
 * @param from 新匹配的起始位置
 * @param to 新匹配的结束位置
 */
static bool append_item_content(match_item_t *item, const char *source, unsigned long long from, unsigned long long to)
{
    if (!item || !source || from > to) {
        return false;
    }

    // 计算新内容的长度和当前内容的长度
    size_t new_len = (to > from) + item->matched_len + 2;
    if (new_len < 0) {
        return false;
    }

    char *new_content = (char *)calloc(1, new_len);
    if (!new_content) {
        return false;
    }

    memcpy(new_content, item->matched, item->matched_len);
    new_content[item->matched_len] = ';';

    memcpy(new_content + item->matched_len + 1, source + from, to - from);
    new_content[new_len - 1] = '\0';

    if (item->matched) {
        free(item->matched);
        item->matched = NULL;
    }

    item->matched = new_content;
    item->matched_len = new_len - 1;
    item->count++;

    return true;
}

/**
 * @brief 在结果中查找匹配项（精确或重叠），如果找到则更新或增加计数。
 *
 * @param res 匹配结果结构体
 * @param index 要查找的规则索引。
 * @param from 新匹配的起始位置。
 * @param to 新匹配的结束位置。
 * @return match_item_t* 如果找到（并可能已更新）现有匹配项，则返回该项的指针；否则返回NULL。
 */
static match_item_t *check_match_item(hs_result_t *res, unsigned int id, unsigned long long from, unsigned long long to)
{
    for (size_t i = 0; i < cvector_size(res->match_items); i++) {
        match_item_t *item = res->match_items[i];
        if (!item) {
            continue;
        }

        // 必须是相同的规则ID
        if (item->index != id) {
            continue;
        }

        if (item->from == from && item->to == to) {
            // 情况1：精确匹配（起始和结束相同）
            return item;
        } else if (item->from < to && from < item->to) { // 重叠条件: item->from < to AND from < item->to
            // 情况2：交叉匹配，取最长者
            uint64_t new_from = min_u64(item->from, from);
            uint64_t new_to = max_u64(item->to, to);

            item->from = new_from;
            item->to = new_to;
            update_item_content(item, res->data, new_from, new_to);

            // 即使现有匹配更长或相等，我们也认为它已被处理。增加计数，因为找到了另一个（重叠的）实例。
            item->count++;
            return item; // 找到重叠匹配
        } else { // 不重叠，追加新匹配内容
            // 情况3：规则ID相同但不重叠，追加新匹配内容
            append_item_content(item, res->data, from, to);
            return item;
        }
    }

    // 未找到具有相同规则ID和精确/重叠边界的现有项
    return NULL;
}

/**
 * @brief Hyperscan 匹配事件回调处理函数
 * 当 Hyperscan 找到匹配项时调用此函数。
 * 它负责查找或创建 match_item_t，更新内容和计数。
 *
 * @param id Hyperscan 引擎内部的规则 ID。
 * @param from 匹配项在数据流中的起始偏移量。
 * @param to 匹配项在数据流中的结束偏移量（不包含）。
 * @param flags Hyperscan 提供的标志（当前未使用）。
 * @param arg 用户提供的上下文指针 (应为 hs_result_t*)。
 * @return 返回 0 继续扫描，返回非 0 停止扫描。
 */
int hs_match_cb(unsigned int id, unsigned long long from, unsigned long long to, unsigned int flags, void *arg)
{
    hs_result_t *res = (hs_result_t *)arg;
    if (!res || !res->data || !res->hs_items || !res->hs_items[id]) {
        return -1;
    }

    // 检查此项匹配（或同一规则的重叠匹配）是否已存在
    match_item_t *item = check_match_item(res, id, from, to);
    if (item) { // 已找到并更新（或仅增加计数）。无需添加新项。
        return 0; // 继续扫描
    }

    // 添加新的匹配项
    match_item_t *new_item = (match_item_t *)calloc(1, sizeof(match_item_t));
    if (!new_item) {
        return -1;
    }

    // 从原始规则信息中填充数据
    hs_item_t *source_item = (hs_item_t *)res->hs_items[id];
    new_item->attrb = source_item->attrb;
    new_item->index = id;

    // 初始化匹配元数据
    new_item->from = from;
    new_item->to = to;
    new_item->count = 1;

    new_item->key = strdup(source_item->key);
    new_item->name = strdup(source_item->name);

    // 设置边界并复制内容
    update_item_content(new_item, res->data, from, to);

    // 添加到匹配项容器
    cvector_push_back(res->match_items, new_item);

    return 0; // 继续扫描
}

void hs_match_item_free(void *item_obj)
{
    match_item_t *item = (match_item_t *)item_obj;
    if (!item) return;

    if (item->key) {
        free(item->key);
        item->key = NULL;
    }

    if (item->name) {
        free(item->name);
        item->name = NULL;
    }

    if (item->matched) {
        free(item->matched);
        item->matched = NULL;
    }

    if (item->extract) {
        free(item->extract);
        item->extract = NULL;
    }

    free(item);
}

/**
 * @brief 将 Hyperscan 错误码转换为可读字符串
 *
 * 该函数将 Hyperscan 的错误码（hs_error_t）转换为对应的英文描述字符串，便于日志和调试。
 * 注意：如果 Hyperscan 版本有新增错误码，请及时补充。
 *
 * @param err Hyperscan 错误码
 * @return 对应的错误描述字符串（英文）
 */
const char *hs_error_info(hs_error_t err)
{
    switch (err) {
        case HS_SUCCESS:              return "Success";
        case HS_INVALID:              return "Invalid parameter";
        case HS_NOMEM:                return "Unable to allocate memory";
        case HS_SCAN_TERMINATED:      return "Scan terminated by callback";
        case HS_COMPILER_ERROR:       return "Compiler error";
        case HS_DB_VERSION_ERROR:     return "Database version mismatch";
        case HS_DB_PLATFORM_ERROR:    return "Database platform mismatch";
        case HS_DB_MODE_ERROR:        return "Database mode mismatch";
        case HS_BAD_ALIGN:            return "Bad alignment";
        case HS_BAD_ALLOC:            return "Bad allocation";
        case HS_SCRATCH_IN_USE:       return "Scratch space is in use";
        case HS_ARCH_ERROR:           return "Unsupported architecture";
        case HS_INSUFFICIENT_SPACE:   return "Insufficient space";
        #ifdef HS_STREAM_ERROR
        case HS_STREAM_ERROR:         return "Stream error";
        #endif
        #ifdef HS_STATE_ERROR
        case HS_STATE_ERROR:          return "State error";
        #endif
        default:                      return "Unknown Hyperscan error";
    }
}


