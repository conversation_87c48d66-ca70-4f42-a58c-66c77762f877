#include "util_ip.h"
#include "db_proc_business.h"

static int business_plugin_id = -1;

/**
 * @brief 添加域名到哈希表
 *
 * @param domains 域名字符串
 * @param hash 哈希表
 */
static void util_add_domain_hash(const char *domains, khash_t(domain_hash) *hash)
{
    if (!domains || !hash || strlen(domains) == 0) {
        return;
    }

    int rslt_cnt = 0;
    sds *rslt = sdssplitlen(domains, strlen(domains), ";", 1, &rslt_cnt);
    if (!rslt) {
        return;
    }

    for (int i = 0; i < rslt_cnt; i++) {
        if (rslt[i] && strlen(rslt[i]) > 0) {
            int ret;
            sds domain_key = sdsdup(rslt[i]);  // 创建key的副本
            khiter_t k = kh_put(domain_hash, hash, domain_key, &ret);
            if (ret > 0) { // key不存在，插入成功
                kh_key(hash, k) = domain_key;  // 存储key
                kh_val(hash, k) = domain_key;  // value和key使用同一个sds
            } else if (ret == 0) { // key已存在
                sdsfree(domain_key);  // 释放未使用的副本
            } else { // 插入失败
                sdsfree(domain_key);
            }
        }
    }
    sdsfreesplitres(rslt, rslt_cnt);
}

static int business_tdata_destroy(const void *arg)
{
    if (!arg) {
        return CFG_SUCCESS;
    }

    business_tdata_t *tdata = (business_tdata_t *)arg;

    // 释放通用部分
    cvector_free(tdata->items);

    // 释放 business 特有部分（这里没有）

    free(tdata);
    return CFG_SUCCESS;
}

void business_item_destroy(void *arg)
{
    if (!arg) {
        return;
    }

    business_item_t *item = (business_item_t *)arg;
    if (item->key) {
        sdsfree(item->key);
    }

    if (item->name) {
        sdsfree(item->name);
    }

    if (item->belong) {
        sdsfree(item->belong);
    }

    if (item->network_type) {
        sdsfree(item->network_type);
    }

    if (item->ip_tree) {
        util_free_ip_tree(item->ip_tree);
    }

    // 释放域名哈希表
    if (item->domains) {
        khiter_t k;
        for (k = kh_begin(item->domains); k != kh_end(item->domains); ++k) {
            if (kh_exist(item->domains, k)) {
                sdsfree((char *)kh_key(item->domains, k));  // 释放key
                // value和key是同一个sds，不需要重复释放
            }
        }
        kh_destroy(domain_hash, item->domains);
    }

    free(item);
    return;
}

static int business_item_create(items_t *items, PGresult *res, size_t row)
{
    business_item_t *item = (business_item_t *)calloc(1, sizeof(business_item_t));
    if (!item) {
        return CFG_FAILURE;
    }

    item->key = sdsnew(PQgetvalue(res, row, 0));
    item->name = sdsnew(PQgetvalue(res, row, 1));
    item->belong = sdsnew(PQgetvalue(res, row, 2));
    item->network_type = sdsnew(PQgetvalue(res, row, 3));

    if (!item->key || !item->name || !item->belong || !item->network_type) {
        free(item);
        return CFG_FAILURE;
    }

    // 处理IP地址
    const char *ips = PQgetvalue(res, row, 4);
    if (ips && strlen(ips) > 0) {
        util_add_ip_tree(ips, item->ip_tree);
    }

    // 处理域名
    const char *domains = PQgetvalue(res, row, 5);
    if (domains && strlen(domains) > 0) {
        // 初始化域名哈希表
        item->domains = kh_init(domain_hash);
        if (!item->domains) {
            free(item);
            return CFG_FAILURE;
        }
        util_add_domain_hash(domains, item->domains);
    }

    cvector_push_back(*items, item);

    return CFG_SUCCESS;
}

// business_item_t的深拷贝
static business_item_t *business_item_deep_copy(const business_item_t *item)
{
    if (!item) {
        return NULL;
    }

    business_item_t *new_item = kcalloc(1, sizeof(business_item_t));
    if (!new_item) {
        return NULL;
    }

    // 深拷贝sds字符串
    if (item->key) {
        new_item->key = sdsdup(item->key);
        if (!new_item->key) {
            goto cleanup;
        }
    }

    if (item->name) {
        new_item->name = sdsdup(item->name);
        if (!new_item->name) {
            goto cleanup;
        }
    }

    if (item->belong) {
        new_item->belong = sdsdup(item->belong);
        if (!new_item->belong) {
            goto cleanup;
        }
    }

    if (item->network_type) {
        new_item->network_type = sdsdup(item->network_type);
        if (!new_item->network_type) {
            goto cleanup;
        }
    }

    new_item->ip_tree = NULL;

    new_item->domains = NULL;

    return new_item;

cleanup:
    // 清理已分配的资源
    business_item_destroy(new_item);
    return NULL;
}

static int business_tdata_post_process(void *tdata)
{
    return CFG_SUCCESS;
}

static const cfg_module_create_t bs_module = {
    .expect_cols = API_BS_COL_NUM,
    .table_name = API_BS_TABLE,
    .redis_key = API_REDIS_KEY_BS,
    .tdata_size = sizeof(business_tdata_t),
    .item_size = sizeof(business_item_t),
    .create_item = business_item_create,
    .destroy_item = business_item_destroy,
    .post_process = business_tdata_post_process,
    .destroy_tdata = business_tdata_destroy,
};

static void *business_tdata_create(void *arg)
{
    return cfg_module_create((void *)&bs_module);
}

static config_plugin_t business_plugin = {
    .name = "business",
    .check_interval = 1,
    .grace_period = 3,
    .create = business_tdata_create,
    .destroy = business_tdata_destroy,
};

int business_config_init()
{
    business_plugin_id = an_cfg_plugin_register(&business_plugin);
    return business_plugin_id;
}

int get_business_plugin_id()
{
    return business_plugin_id;
}

/**
 * @brief 根据域名获取业务项
 *
 * @param domain 域名
 * @return const business_item_t* 业务项
 */
const business_item_t *get_business_item_by_domain(const char *domain)
{
    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(business_plugin_id);
    if (!domain || !cfg_wrap || !cfg_wrap->config) {
        return NULL;
    }
    business_tdata_t *tdata = (business_tdata_t *)cfg_wrap->config;

    business_item_t *result = NULL;

    for (size_t i = 0; i < cvector_size(tdata->items); i++) {
        business_item_t *item = (business_item_t *)tdata->items[i];
        if (!item || !item->key || !item->domains) {
            continue;
        }

        khiter_t k = kh_get(domain_hash, item->domains, domain);
        if (k != kh_end(item->domains)) {
            result = business_item_deep_copy(item);
            break;
        }
    }

    an_cfg_wrapper_put(business_plugin_id, cfg_wrap);

    return result;
}

const business_item_t *get_business_item_by_ip(const char *ip)
{
    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(business_plugin_id);
    if (!ip || !cfg_wrap || !cfg_wrap->config) {
        return NULL;
    }
    business_tdata_t *tdata = (business_tdata_t *)cfg_wrap->config;

    business_item_t *result = NULL;

    for (size_t i = 0; i < cvector_size(tdata->items); i++) {
        business_item_t *item = (business_item_t *)tdata->items[i];
        if (!item || !item->key || !item->ip_tree) {
            continue;
        }
        if (util_match_ip(item->ip_tree, (char *)ip)) {
            result = business_item_deep_copy(item);
            break;
        }
    }

    an_cfg_wrapper_put(business_plugin_id, cfg_wrap);

    return result;
}

/**
 * @brief 获取业务项中的所有域名
 *
 * @param item 业务项
 * @param domains 用于存储域名的数组
 * @param count 返回域名数量
 * @return int 成功返回0，失败返回-1
 */
int get_all_domains_of_business_item(const business_item_t *item, sds **domains, size_t *count)
{
    if (!item || !domains || !count || !item->domains) {
        return CFG_FAILURE;
    }

    // 计算域名数量
    size_t domain_count = kh_size(item->domains);
    if (domain_count == 0) {
        *domains = NULL;
        *count = 0;
        return CFG_SUCCESS;
    }

    // 分配内存
    *domains = (sds *)malloc(domain_count * sizeof(sds));
    if (!*domains) {
        return CFG_FAILURE;
    }

    // 复制域名
    size_t idx = 0;
    khiter_t k;
    for (k = kh_begin(item->domains); k != kh_end(item->domains); ++k) {
        if (kh_exist(item->domains, k)) {
            (*domains)[idx++] = sdsdup(kh_val(item->domains, k));
        }
    }

    *count = domain_count;
    return CFG_SUCCESS;
}
