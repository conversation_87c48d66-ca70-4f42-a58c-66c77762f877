#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>

#include "hiredis.h"
#include "util_redis.h" // 包含我们自己的头文件

// 宏定义常量
#define REDIS_CONNECT_TIMEOUT_SEC 2
#define REDIS_PONG_REPLY "PONG"

// 内部结构体，对外部隐藏实现细节
struct redis_conn_t {
    redisContext *ctx;
};

/**
 * @brief 检查Redis连接状态
 * @param conn Redis连接句柄
 * @return 成功返回0，失败返回-1
 */
#define CHECK_CONN(conn) \
    if (!conn || !conn->ctx || conn->ctx->err) { \
        if (conn && conn->ctx && conn->ctx->errstr) { \
            fprintf(stderr, "Redis connection error: %s", conn->ctx->errstr); \
        } else { \
            fprintf(stderr, "Invalid Redis connection object."); \
        } \
        return -1; \
    }

 /**
  * @brief 检查Redis参数
  * @param conn Redis连接句柄
  * @param key 键名
  * @param value 值
  * @return 成功返回0，失败返回-1
  */
#define CHECK_PARAMS(conn, key, value) \
    if (!conn || !key || !value) { \
        return -1; \
    }

  /**
   * @brief 处理获取字符串类型的 reply
   * @param reply Redis回复
   * @param value 用于存储返回的值的缓冲区
   * @param value_len 缓冲区长度
   * @return 成功返回0，失败返回-1
   */
static int handle_string_reply(redisReply *reply, char *value, int value_len)
{
    int ret = 0;
    switch (reply->type) {
    case REDIS_REPLY_STRING: {
        size_t copy_len = reply->len < (size_t)(value_len - 1) ? reply->len : (size_t)(value_len - 1);
        memcpy(value, reply->str, copy_len);
        value[copy_len] = '\0';
        break;
    }
    case REDIS_REPLY_NIL:
        value[0] = '\0';
        break;
    case REDIS_REPLY_ERROR:
        fprintf(stderr, "Redis command error: %s", reply->str);
        ret = -1;
        break;
    default:
        ret = -1;
        break;
    }
    return ret;
}

redis_conn_t *redis_open(const char *ip, int port, const char *pwd, int db)
{
    redisReply *reply = NULL;
    struct timeval timeout = {REDIS_CONNECT_TIMEOUT_SEC, 0};

    redisContext *ctx = redisConnectWithTimeout(ip, port, timeout);
    if (ctx == NULL || ctx->err) {
        if (ctx) {
            fprintf(stderr, "Redis connection error: %s", ctx->errstr);
            redisFree(ctx);
        } else {
            fprintf(stderr, "Can't allocate redis context");
        }
        return NULL;
    }

    // --- 使用 Pipeline 优化 AUTH 和 SELECT ---
    int need_auth = (pwd && strlen(pwd) > 0);
    if (need_auth) {
        redisAppendCommand(ctx, "AUTH %s", pwd);
    }
    redisAppendCommand(ctx, "SELECT %d", db);

    // 1. 处理 AUTH 结果
    if (need_auth) {
        if (redisGetReply(ctx, (void **)&reply) != REDIS_OK || reply == NULL || reply->type == REDIS_REPLY_ERROR) {
            fprintf(stderr, "Authentication failed: %s", reply ? reply->str : ctx->errstr);
            goto error;
        }
        freeReplyObject(reply);
        reply = NULL;
    }

    // 2. 处理 SELECT 结果
    if (redisGetReply(ctx, (void **)&reply) != REDIS_OK || reply == NULL || reply->type == REDIS_REPLY_ERROR) {
        fprintf(stderr, "Select database failed: %s", reply ? reply->str : ctx->errstr);
        goto error;
    }
    freeReplyObject(reply);

    // 封装到我们自己的结构体中
    redis_conn_t *conn = (redis_conn_t *)malloc(sizeof(redis_conn_t));
    if (!conn) {
        goto error;
    }
    conn->ctx = ctx;

    return conn;

error:
    if (reply) freeReplyObject(reply);
    redisFree(ctx);
    return NULL;
}

void redis_close(redis_conn_t *conn)
{
    if (conn) {
        redisFree(conn->ctx);
        free(conn);
    }
}

int redis_ping(redis_conn_t *conn)
{
    CHECK_CONN(conn);
    redisReply *reply = (redisReply *)redisCommand(conn->ctx, "PING");
    if (reply == NULL) {
        fprintf(stderr, "PING command failed (no reply): %s", conn->ctx->errstr);
        return -1;
    }

    int ret = 0;
    if (reply->type != REDIS_REPLY_STATUS || strcasecmp(reply->str, REDIS_PONG_REPLY) != 0) {
        ret = -1;
    }
    freeReplyObject(reply);
    return ret;
}

/**
 * @brief 设置Redis键值
 * @param redis Redis连接句柄
 * @param key 键名
 * @param value 值
 * @return 成功返回0，失败返回-1
 */
int redis_set(redis_conn_t *conn, const char *key, const char *value)
{
    CHECK_PARAMS(conn, key, value);
    CHECK_CONN(conn);

    redisReply *reply = (redisReply *)redisCommand(conn->ctx, "SET %s %s", key, value);
    if (reply == NULL) {
        fprintf(stderr, "SET command failed (no reply): %s", conn->ctx->errstr);
        return -1;
    }

    int ret = 0;
    if (reply->type != REDIS_REPLY_STATUS || strcasecmp(reply->str, "OK") != 0) {
        fprintf(stderr, "SET command failed: %s", reply->str ? reply->str : "unknown error");
        ret = -1;
    }

    freeReplyObject(reply);
    return ret;
}

/**
 * @brief 从Redis中获取一个值
 * @param redis Redis连接句柄
 * @param key 键名
 * @param value 用于存储返回的值的缓冲区
 * @param value_len 缓冲区长度
 * @return 成功返回0，失败返回-1
 */
int redis_get(redis_conn_t *conn, const char *key, char *value, int value_len)
{
    if (!conn || !key || !value || value_len <= 0) return -1;
    CHECK_CONN(conn);

    redisReply *reply = (redisReply *)redisCommand(conn->ctx, "GET %s", key);
    if (reply == NULL) {
        fprintf(stderr, "GET command failed (no reply): %s", conn->ctx->errstr);
        return -1;
    }

    int ret = handle_string_reply(reply, value, value_len);
    freeReplyObject(reply);
    return ret;
}

/**
 * @brief 从Redis Set中随机返回一个值
 * @param redis Redis连接句柄
 * @param key Set的键名
 * @param value 用于存储返回的值的缓冲区
 * @param value_len 缓冲区长度
 * @return 成功返回0，失败返回-1
 */
int redis_srandmember(redis_conn_t *conn, const char *key, char *value, int value_len)
{
    if (!conn || !key || !value || value_len <= 0) return -1;
    CHECK_CONN(conn);

    redisReply *reply = (redisReply *)redisCommand(conn->ctx, "SRANDMEMBER %s", key);
    if (reply == NULL) {
        fprintf(stderr, "SRANDMEMBER command failed (no reply): %s", conn->ctx->errstr);
        return -1;
    }

    int ret = handle_string_reply(reply, value, value_len);
    freeReplyObject(reply);
    return ret;
}

/**
 * @brief 从Redis Set中弹出一个值
 * @param redis Redis连接句柄
 * @param key Set的键名
 * @param value 用于存储返回的值的缓冲区
 * @param value_len 缓冲区长度
 * @return 成功返回0，失败返回-1
 */
int redis_spop(redis_conn_t *conn, const char *key, char *value, int value_len)
{
    if (!conn || !key || !value || value_len <= 0) return -1;
    CHECK_CONN(conn);

    redisReply *reply = (redisReply *)redisCommand(conn->ctx, "SPOP %s", key);
    if (reply == NULL) {
        fprintf(stderr, "SPOP command failed (no reply): %s", conn->ctx->errstr);
        return -1;
    }

    int ret = handle_string_reply(reply, value, value_len);
    freeReplyObject(reply);
    return ret;
}

/**
 * @brief 从Redis Set中删除一个值
 * @param redis Redis连接句柄
 * @param key Set的键名
 * @param value 要删除的值
 * @return 成功返回0，失败返回-1
 */
int redis_srem(redis_conn_t *conn, const char *key, const char *value)
{
    CHECK_PARAMS(conn, key, value);
    CHECK_CONN(conn);

    redisReply *reply = (redisReply *)redisCommand(conn->ctx, "SREM %s %s", key, value);
    if (reply == NULL) {
        fprintf(stderr, "SREM command failed (no reply): %s", conn->ctx->errstr);
        return -1;
    }

    int ret = 0;
    // SREM 成功返回一个整型，表示删除的元素数量
    if (reply->type != REDIS_REPLY_INTEGER) {
        fprintf(stderr, "SREM command error: unexpected reply type %d", reply->type);
        ret = -1;
    }

    freeReplyObject(reply);
    return ret;
}

/**
 * @brief 连接Redis，检查指定Set中是否存在某个成员，如果存在则移除它。
 *
 * 该函数通过一次 SREM 命令原子地完成检查和移除操作。
 *
 * @param ip Redis服务器IP地址。
 * @param port Redis服务器端口。
 * @param pwd Redis密码，如果没有则为NULL或空字符串。
 * @param db Redis数据库编号。
 * @param set_key Set的键名。
 * @param target_value 要检查并移除的目标值。
 * @return 如果目标值存在于Set中并被成功移除，返回 0。
 *         如果目标值不存在，或发生任何错误（如连接失败、命令失败），返回 -1。
 */
int srem_redis_key(const char *ip, int port, const char *pwd, int db, const char *set_key, const char *target_value)
{
    // 1. 参数校验
    if (!ip || !set_key || !target_value) {
        fprintf(stderr, "Invalid parameters provided.\n");
        return -1;
    }

    // 2. 连接 Redis
    redis_conn_t *conn = redis_open(ip, port, pwd, db);
    if (!conn) {
        return -1;
    }

    // --- 核心逻辑 ---
    int result_code = -1; // 默认返回 -1 (未找到/未移除/错误)
    redisReply *reply = NULL;

    // 3. 使用 SREM 原子地检查并移除成员
    // SREM key member [member ...]
    // 返回被成功移除的成员的数量，不包括被忽略的成员。
    reply = (redisReply *)redisCommand(conn->ctx, "SREM %s %s", set_key, target_value);

    // 4. 检查命令执行结果
    if (reply == NULL) {
        fprintf(stderr, "SREM command failed (no reply): %s\n", conn->ctx->errstr);
        // 错误情况，result_code 保持 -1
    } else if (reply->type != REDIS_REPLY_INTEGER) {
        fprintf(stderr, "SREM command error: unexpected reply type %d\n", reply->type);
        // 错误情况，result_code 保持 -1
    } else {
        // 命令成功执行，检查 SREM 的返回值
        if (reply->integer == 1) {
            // SREM 返回 1 表示成功移除了 1 个元素
            // 这就证明了成员之前是存在的！
            result_code = 0; // 成功！
        } else {
            // SREM 返回 0 表示成员不存在，没有东西被移除
            result_code = -1; // 未找到
        }
    }

    // 5. 释放资源
    if (reply) {
        freeReplyObject(reply);
    }

    // 6. 关闭连接
    redis_close(conn);

    return result_code;
}
