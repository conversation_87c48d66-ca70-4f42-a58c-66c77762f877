#ifndef __UTIL_REDIS_H__
#define __UTIL_REDIS_H__

// 使用一个不透明的结构体指针，隐藏内部实现，增加类型安全
typedef struct redis_conn_t redis_conn_t;

/**
 * @brief 打开Redis连接
 * @param ip Redis服务器IP地址
 * @param port Redis服务器端口
 * @param pwd Redis密码
 * @param db Redis数据库编号
 * @return 成功返回Redis连接句柄，失败返回NULL
 */
redis_conn_t *redis_open(const char *ip, int port, const char *pwd, int db);

/**
 * @brief 关闭Redis连接
 * @param conn Redis连接句柄
 */
void redis_close(redis_conn_t *conn);

/**
 * @brief 检查Redis连接是否正常
 * @param conn Redis连接句柄
 * @return 成功返回0，失败返回-1
 */
int redis_ping(redis_conn_t *conn);

/**
 * @brief 设置Redis键值
 * @param redis Redis连接句柄
 * @param key 键名
 * @param value 值
 * @return 成功返回0，失败返回-1
 */
int redis_set(redis_conn_t *conn, const char *key, const char *value);

/**
 * @brief 获取Redis键值
 * @param redis Redis连接句柄
 * @param key 键名
 * @param value 值
 * @param value_len 值长度
 * @return 获取成功返回0，失败返回-1
 */
int redis_get(redis_conn_t *conn, const char *key, char *value, int value_len);

/**
 * @brief 从Redis Set中随机返回一个值
 * @param redis Redis连接句柄
 * @param key Set的键名
 * @param value 用于存储返回的值的缓冲区
 * @param value_len 缓冲区长度
 * @return 成功返回0，失败返回-1
 */
int redis_srandmember(redis_conn_t *conn, const char *key, char *value, int value_len);

/**
 * @brief 从Redis Set中随机弹出一个值
 * @param redis Redis连接句柄
 * @param key Set的键名
 * @param value 用于存储返回的值的缓冲区
 * @param value_len 缓冲区长度
 * @return 成功返回0，失败返回-1
 */
int redis_spop(redis_conn_t *conn, const char *key, char *value, int value_len);

/**
 * @brief 从Redis Set中删除一个值
 * @param redis Redis连接句柄
 * @param key Set的键名
 * @param value 要删除的值
 * @return 成功返回0，失败返回-1
 */
int redis_srem(redis_conn_t *conn, const char *key, const char *value);

/**
 * @brief 连接Redis，检查指定Set中是否存在某个成员，如果存在则移除它。
 *
 * 该函数通过一次 SREM 命令原子地完成检查和移除操作。
 *
 * @param ip Redis服务器IP地址。
 * @param port Redis服务器端口。
 * @param pwd Redis密码，如果没有则为NULL或空字符串。
 * @param db Redis数据库编号。
 * @param set_key Set的键名。
 * @param target_value 要检查并移除的目标值。
 * @return 如果目标值存在于Set中并被成功移除，返回 0。
 *         如果目标值不存在，或发生任何错误（如连接失败、命令失败），返回 -1。
 */
int srem_redis_key(const char *ip, int port, const char *pwd, int db, const char *set_key, const char *target_value);

// 如果需要支持二进制数据，可以添加如下函数
// int redis_set_bin(redis_conn_t *conn, const char *key, size_t key_len, const char *val, size_t val_len);
// int redis_get_bin(redis_conn_t *conn, const char *key, size_t key_len, char *val_buf, size_t *val_len);

#endif // __UTIL_REDIS_H__
