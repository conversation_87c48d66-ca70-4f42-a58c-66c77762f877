#include "khash.h"
#include "cfg_util.h"
#include "db_proc_oper.h"

static int oper_plugin_id = -1;

KHASH_MAP_INIT_STR(oper_hash, oper_item_t *)

static int oper_tdata_destroy(const void *arg)
{
    if (!arg) {
        return CFG_SUCCESS;
    }

    oper_tdata_t *tdata = (oper_tdata_t *)arg;

    // 释放向量（会自动调用oper_item_destroy释放每个item）
    cvector_free(tdata->items);

    // 释放哈希表（不需要释放值，因为它们是指向items中元素的指针）
    khash_t(oper_hash) *hash = (khash_t(oper_hash) *)tdata->ctx;
    if (hash) {
        kh_destroy(oper_hash, hash);
    }

    free(tdata);
    return CFG_SUCCESS;
}

static void oper_item_destroy(void *arg)
{
    if (!arg) {
        return;
    }

    oper_item_t *item = (oper_item_t *)arg;

    if (item->key) {
        sdsfree(item->key);
        item->key = NULL;
    }

    if (item->proto) {
        sdsfree(item->proto);
        item->proto = NULL;
    }

    if (item->direction) {
        sdsfree(item->direction);
        item->direction = NULL;
    }

    if (item->fields) {
        sdsfree(item->fields);
        item->fields = NULL;
    }

    if (item->expr) {
        sdsfree(item->expr);
        item->expr = NULL;
    }

    if (item->depend) {
        sdsfree(item->depend);
        item->depend = NULL;
    }

    item->match = -1;

    free(item);
}

static int oper_item_create(items_t *items, PGresult *res, size_t row)
{
    oper_item_t *item = (oper_item_t *)calloc(1, sizeof(oper_item_t));
    if (!item) {
        return CFG_FAILURE;
    }

    item->key = sdsnew(safe_get_value(res, row, 0));
    item->proto = sdsnew(safe_get_value(res, row, 1));
    item->direction = sdsnew(safe_get_value(res, row, 2));
    item->fields = sdsnew(safe_get_value(res, row, 3));
    item->match = cfg_str_to_int(safe_get_value(res, row, 4));
    item->expr = sdsnew(safe_get_value(res, row, 5));
    item->depend = sdsnew(safe_get_value(res, row, 6));

    if (!item->key || !item->proto || !item->direction || !item->fields || !item->expr) {
        oper_item_destroy(item);
        return CFG_FAILURE;
    }

    cvector_push_back(*items, item);

    return CFG_SUCCESS;
}

static int oper_tdata_post_process(void *arg)
{
    oper_tdata_t *tdata = (oper_tdata_t *)arg;
    if (!tdata->items) {
        return CFG_SUCCESS;
    }

    // 创建哈希表
    khash_t(oper_hash) *hash = kh_init(oper_hash);
    if (!hash) {
        return CFG_FAILURE;
    }

    // 将所有item添加到哈希表
    for (size_t i = 0; i < cvector_size(tdata->items); i++) {
        oper_item_t *item = (oper_item_t *)tdata->items[i];
        if (!item || !item->key) {
            continue;
        }

        int ret;
        khiter_t k = kh_put(oper_hash, hash, item->key, &ret);
        if (ret > 0) { // 插入成功
            kh_val(hash, k) = item;
        } else { // 插入失败
            kh_destroy(oper_hash, hash);
            return CFG_FAILURE;
        }
    }

    // 存储哈希表
    tdata->ctx = hash;
    return CFG_SUCCESS;
}

static const cfg_module_create_t module_desc = {
    .expect_cols = API_OPER_COL_NUM,
    .table_name = API_OPER_TABLE,
    .redis_key = API_REDIS_KEY_OPER,
    .tdata_size = sizeof(oper_tdata_t),
    .item_size = sizeof(oper_item_t),
    .create_item = oper_item_create,
    .destroy_item = oper_item_destroy,
    .post_process = oper_tdata_post_process,
    .destroy_tdata = oper_tdata_destroy,
};

static void *oper_tdata_create(void *arg)
{
    return cfg_module_create((void *)&module_desc);
}

static config_plugin_t oper_plugin = {
    .name = "oper",
    .check_interval = 1,
    .grace_period = 3,
    .create = oper_tdata_create,
    .destroy = oper_tdata_destroy,
};

int oper_config_init(void)
{
    oper_plugin_id = an_cfg_plugin_register(&oper_plugin);
    return oper_plugin_id;
}

int get_oper_plugin_id(void)
{
    return oper_plugin_id;
}

oper_item_t *get_oper_item_by_key(const char *key)
{
    if (!key || oper_plugin_id < 0) {
        return NULL;
    }

    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(oper_plugin_id);
    CHECK_ERR_EXEC(!cfg_wrap || !cfg_wrap->config, CFG_LOG_TAG, return NULL, "Invalid cfg_wrap or config");

    oper_tdata_t *tdata = (oper_tdata_t *)cfg_wrap->config;
    khash_t(oper_hash) *hash = (khash_t(oper_hash) *)tdata->ctx;
    if (!hash) {
        an_cfg_wrapper_put(oper_plugin_id, cfg_wrap);
        return NULL;
    }

    khiter_t k = kh_get(oper_hash, hash, key);
    if (k == kh_end(hash)) {
        an_cfg_wrapper_put(oper_plugin_id, cfg_wrap);
        return NULL;
    }

    oper_item_t *item = kh_val(hash, k);

    an_cfg_wrapper_put(oper_plugin_id, cfg_wrap);

    return item;
}
