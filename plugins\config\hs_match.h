#ifndef __HS_MATCH_H__
#define __HS_MATCH_H__

#include "hs_create.h"
#include "cfg_util.h"

typedef struct {
    char *key;  // 规则ID
    char *name; // 规则名称
    char *matched; // 匹配到的内容，多个匹配内容用.分隔
    size_t matched_len; // 匹配到的内容长度

    uint16_t attrb; // 属性:0-不提取，1-提取账号，2-提取密码，3-提取token
    char *extract;  // 提取内容，多个提取内容用.分隔
    size_t extract_len; // 提取内容长度

    uint64_t to;    // 命中结束位置
    uint64_t from;  // 命中起始位置
    uint32_t index; // 命中规则下标
    uint16_t count; // 命中次数
} match_item_t;

typedef struct {
    const char *data;
    uint64_t data_len;

    items_t hs_items; // 原始item容器
    cvector_vector_type(match_item_t *) match_items; // 匹配到的item容器
} hs_result_t;

int hs_match_cb(unsigned int id, unsigned long long from, unsigned long long to, unsigned int flags, void *arg);

const char *hs_error_info(hs_error_t err);

void hs_match_item_free(void *item_obj);

#endif // __HS_MATCH_H__