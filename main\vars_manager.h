#ifndef __VARS_MANAGER_H__
#define __VARS_MANAGER_H__

#include "plugin_api.h"
#include "workflow.h"

#include <khash.h>
#include <sds.h>
#include <an_calc.h>
#include <stdbool.h>

// 哈希表类型定义 - 必须在使用前定义
KHASH_MAP_INIT_STR(str, void *)

// 变量类型枚举
typedef enum {
    VAR_CATEGORY_BASE = 0,      // 基础变量
    VAR_CATEGORY_DYNAMIC,       // 动态变量
    VAR_CATEGORY_OPERATOR,      // 算子输出变量
    VAR_CATEGORY_ERROR          // 错误变量
} var_category_t;

// 统一变量配置结构
typedef struct {
    sds name;                           // 变量名
    an_calc_variable_type_t type;       // 变量类型
    var_category_t category;            // 变量分类
    int index;                          // 变量索引（缓存）
    bool is_registered;                 // 是否已注册
} var_config_t;

// 字段映射配置
typedef struct {
    sds field_name;                     // 字段名
    sds result_var;                     // 对应的结果变量
} field_mapping_t;

// 算子配置结构
typedef struct {
    sds instance_name;                  // 算子实例名
    sds operator_name;                  // 算子名称
    an_operator_binding_t *bindings;    // 绑定配置
    size_t binding_count;               // 绑定数量
    bool is_registered;                 // 是否已注册
} operator_config_t;

// 统一配置管理器
typedef struct {
    khash_t(str) *var_configs;          // 变量配置哈希表 key:var_name, value:var_config_t*
    khash_t(str) *field_mappings;       // 字段映射哈希表 key:field_name, value:field_mapping_t*
    khash_t(str) *operator_configs;     // 算子配置哈希表 key:instance_name, value:operator_config_t*
    cvector_vector_type(sds) dynamic_vars; // 动态变量列表
} vars_manager_t;

/**
 * @brief 初始化配置管理器
 * @return int 成功返回0，失败返回-1
 */
int vars_manager_init(void);

/**
 * @brief 销毁配置管理器
 */
void vars_manager_destroy(void);

/**
 * @brief 获取配置管理器实例
 * @return vars_manager_t* 配置管理器指针
 */
vars_manager_t *get_vars_manager(void);

/**
 * @brief 添加变量配置
 * @param name 变量名
 * @param type 变量类型
 * @param category 变量分类
 * @return int 成功返回0，失败返回-1
 */
int add_variable_config(const char *name, an_calc_variable_type_t type, var_category_t category);

/**
 * @brief 根据变量名获取变量索引
 * @param var_name 变量名
 * @return int 变量索引，失败返回-1
 */
int get_variable_index(const char *var_name);

/**
 * @brief 根据字段名获取映射变量
 * @param field_name 字段名
 * @return 映射变量
 */
const char *get_field_mapping_var(const char *field_name);

/**
 * @brief 安全注册变量
 * @param engine 计算引擎
 * @param var_name 变量名
 * @param type 变量类型
 * @return int 成功返回0，失败返回-1
 */
int safe_register_variable(an_calc_t *engine, const char *var_name, an_calc_variable_type_t type);

/**
 * @brief 安全注册算子实例
 * @param engine 计算引擎
 * @param instance_name 实例名
 * @param plugin_name 插件名
 * @param bindings 绑定配置
 * @param binding_count 绑定数量
 * @return int 成功返回0，失败返回-1
 */
int safe_register_operator(an_calc_t *engine, const char *instance_name, const char *plugin_name, const an_operator_binding_t *bindings, size_t binding_count);

/**
 * @brief 动态缓存所有变量索引
 * @param ctx 工作流上下文
 */
void cache_all_variable_indices(workflow_ctx_t *ctx);

#endif // __VARS_MANAGER_H__
