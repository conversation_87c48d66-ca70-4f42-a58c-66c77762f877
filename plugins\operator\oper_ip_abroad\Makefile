include $(DSAP_BUILD_MACROS_MK)

#$(error $(shell pkg-config --cflags libxlib) )
CFLAGS_LOCAL += $(shell pkg-config --cflags libxlib)
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_util -I $(INSTALL_ROOT_INCLUDE)/an_calc

#LDFLAGS += $(shell pkg-config --libs hiredis)
#LDFLAGS += -L$(INSTALL_ROOT_LIB)/ -lhiredis

BUILD_TYPE = dynlib
BUILD_NAME = oper_ip_abroad
BUILD_VERSION = 1
#INSTALL_APEEND_PATH =
BUILD_DYNLIB_PGKCONFIG = 1

INCLUDE_FILES +=

include $(DSAP_BUILD_RULES_MK)
