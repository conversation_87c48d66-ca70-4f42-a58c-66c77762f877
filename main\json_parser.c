#include "plugin_api.h"
#include "common.h"
#include <an_common.h>
#include <an_string.h>
#include <an_json.h>
#include <string.h>

#define LOG_TAG "plugin_json_parser"

// --- Context Definition ---

struct json_parser_ctx_s
{
    perf_mempool_t *msg_pool;
    perf_mempool_t *str_pool;
};

#define JSON_ARENA_EXTRACT_STR(parent_obj, key) \
    do { \
        yyjson_val *__val = yyjson_arena_obj_get(parent_obj, #key); \
        if (!__val || !yyjson_is_str(__val)) { \
            LOG_WARN(LOG_TAG, "Failed to get string or type mismatch for key: %s", #key); \
            goto cleanup_msg; \
        } \
        msg->key = an_string_alloc_str(&msg->arena, yyjson_get_str(__val)); \
        if (!msg->key) { \
            LOG_ERROR(LOG_TAG, "Failed to allocate string for key: %s", #key); \
            goto cleanup_msg; \
        } \
    } while (0)

#define JSON_ARENA_EXTRACT_INT(parent_obj, key) \
    do { \
        yyjson_val *__val = yyjson_arena_obj_get(parent_obj, #key); \
        if (!__val || !yyjson_is_int(__val)) { \
            LOG_WARN(LOG_TAG, "Failed to get integer or type mismatch for key: %s", #key); \
            goto cleanup_msg; \
        } \
        msg->key = yyjson_get_int(__val); \
    } while (0)

#define JSON_ARENA_EXTRACT_STR_NAMED(parent_obj, field_name, json_key_str) \
    do { \
        yyjson_val *__val = yyjson_arena_obj_get(parent_obj, json_key_str); \
        if (!__val || !yyjson_is_str(__val)) { \
            LOG_WARN("JSON_EXTRACT", "Failed to get string or type mismatch for key: %s", json_key_str); \
            goto cleanup_msg; \
        } \
        msg->field_name = an_string_alloc_str(&msg->arena, yyjson_get_str(__val)); \
        if (!msg->field_name) { \
            LOG_ERROR("JSON_EXTRACT", "Failed to allocate string for key: %s", json_key_str); \
            goto cleanup_msg; \
        } \
    } while (0)

static int json_parser_process(void *context, int thread_idx, data_packet_t *packet)
{
    (void)thread_idx;
    if (!packet || !packet->data || packet->size == 0) {
        return PIPELINE_ERROR_INVALID_PARAM;
    }

    json_parser_ctx_t *ctx = (json_parser_ctx_t *)context;
    risk_message_t *msg = NULL;
    int ret = PIPELINE_SUCCESS;

    // --- 内存池和竞技场初始化 (保持不变) ---
    msg = perf_mempool_get(ctx->msg_pool);
    if (!msg) return PIPELINE_ERROR_MEMORY;
    memset(msg, 0, sizeof(risk_message_t));

    if (an_string_init(&msg->arena, ctx->str_pool) != 0) {
        perf_mempool_put(ctx->msg_pool, msg);
        return PIPELINE_ERROR_MEMORY;
    }

    // --- 解析和字段提取
    yyjson_arena_t json;
    if (!yyjson_arena_parse(&json, &msg->arena, (const char *)packet->data, packet->size, 0)) {
        LOG_WARN(LOG_TAG, "json parse failed, %s", packet->data);
        ret = PIPELINE_ERROR_PLUGIN_FAILED;
        goto cleanup_msg;
    }

    yyjson_val *root = yyjson_arena_get_root(&json);
    if (!yyjson_is_obj(root)) {
        LOG_WARN(LOG_TAG, "json root is not object");
        ret = PIPELINE_ERROR_PLUGIN_FAILED;
        goto cleanup_msg;
    }

    JSON_ARENA_EXTRACT_STR_NAMED(root, type, "event_type");
    JSON_ARENA_EXTRACT_STR(root, timestamp);

	JSON_ARENA_EXTRACT_STR_NAMED(root, sip, "src_ip");
	JSON_ARENA_EXTRACT_STR_NAMED(root, dip, "dest_ip");
    JSON_ARENA_EXTRACT_INT(root, flow_id);


    // 成功，链接到流水线包
    msg->header.data = msg;
    msg->header.size = sizeof(*msg);
    packet->next = &msg->header;

    return PIPELINE_SUCCESS;

cleanup_msg:
    // 统一的失败清理路径
    if (ret == PIPELINE_SUCCESS)
        ret = PIPELINE_ERROR_PLUGIN_FAILED;
    an_string_release_all(&msg->arena);
    perf_mempool_put(ctx->msg_pool, msg);
    return ret;
}

// --- Public API Implementation ---

json_parser_ctx_t *plugin_json_parser_ctx_create(cvector_vector_type(perf_mempool_t *) global_pools, an_config_node_t *params_node)
{
    (void)params_node; // This plugin doesn't use 'params' from the config.

    json_parser_ctx_t *ctx = calloc(1, sizeof(json_parser_ctx_t));
    if (!ctx)
    {
        LOG_ERROR(LOG_TAG, "Failed to allocate context.");
        return NULL;
    }

    // Find required memory pools from the global list.
    for (size_t i = 0; i < cvector_size(global_pools); ++i)
    {
        if (strcmp(perf_mempool_get_name(global_pools[i]), "msg_pool") == 0)
        {
            ctx->msg_pool = global_pools[i];
        }
        else if (strcmp(perf_mempool_get_name(global_pools[i]), "str_pool") == 0)
        {
            ctx->str_pool = global_pools[i];
        }
    }

    if (!ctx->msg_pool || !ctx->str_pool)
    {
        LOG_ERROR(LOG_TAG, "Required memory pools ('msg_pool', 'str_pool') not found in global resources.");
        free(ctx);
        return NULL;
    }

    return ctx;
}

void plugin_json_parser_ctx_destroy(json_parser_ctx_t *ctx)
{
    free(ctx);
}

plugin_t plugin_json_parser_get(json_parser_ctx_t *ctx)
{
    return (plugin_t){
        .name = "json_parser",
        .thread_process = json_parser_process,
        .context = ctx,
    };
}
