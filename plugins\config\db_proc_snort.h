#ifndef __DB_PROC_SNORT_H__
#define __DB_PROC_SNORT_H__

typedef base_tdata_t snort_tdata_t;

typedef struct {
    int type;         // 1.风险2.攻击3.漏洞
    int state;        // 状态：0.启用,1.禁用
    sds key;          // 规则key
    sds rule_content; // 规则内容
} snort_item_t;

/**
 * @brief snort插件初始化
 *
 * @param ctx 插件上下文
 * @return int 插件ID
 */
int snort_config_init(plugin_ctx_t *ctx);

#endif // __DB_PROC_SNORT_H__