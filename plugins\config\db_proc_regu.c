#include "cfg_util.h"
#include "hs_create.h"
#include "db_proc_regu.h"

static int regu_plugin_id = -1;
static int regu_thread_num = 1;

/**
 * @brief 销毁数据
 *
 * @param arg 参数
 * @return int 成功返回CFG_SUCCESS，失败返回CFG_FAILURE
 */
static int regu_tdata_destroy(const void *arg)
{
    hs_tdata_t *tdata = (hs_tdata_t *)arg;

    hs_database_destroy(tdata);

    cvector_free(tdata->items);

    free(tdata);

    return CFG_SUCCESS;
}

/**
 * @brief 销毁特征项
 *
 * @param arg 特征项
 */
static void regu_item_destroy(void *arg)
{
    hs_item_t *item = (hs_item_t *)arg;
    if (item == NULL) {
        return;
    }

    sdsfree(item->key);

    sdsfree(item->name);

    sdsfree(item->value);

    // pcre的销毁
    if (item->re) {
        if (cvector_size(item->re) > 0 && item->re[0]) {
            pcre_free(item->re[0]);
        }

        // 释放cvector本身
        cvector_free(item->re);
        item->re = NULL;
    }

    free(item);
}

/**
 * @brief 从一行数据库查询结果中创建并填充一个或多个规则项 (hs_item_t)。
 *
 * 此函数处理一行数据，该行的 'value' 字段可能由分隔符分割成多个子规则。
 * 每个子规则都会生成一个新的 hs_item_t 对象。
 *
 * @param tdata_arg 指向目标数据结构 (hs_tdata_t) 的指针，新创建的 item 会被添加到其中。
 * @param res       PostgreSQL 的查询结果集。
 * @param row       当前处理的行号。
 *
 * @return int       成功返回 CFG_SUCCESS，失败返回 CFG_FAILURE。
 */
static int regu_item_create(items_t *items, PGresult *res, size_t row)
{
    int ret_status = CFG_FAILURE; // 默认返回失败

    // 声明所有需要手动清理的资源
    int split_count = 0;
    sds *split_values = NULL;
    sds replacements_str = NULL;

    sds shared_key = NULL;
    sds shared_name = NULL;

    const char *key_str = PQgetvalue(res, row, 0);
    const char *name_str = PQgetvalue(res, row, 1);
    const char *value_str = PQgetvalue(res, row, 2);
    const char *type_str = PQgetvalue(res, row, 3);
    const char *attrb_str = PQgetvalue(res, row, 4);
    const char *replace_src_str = PQgetvalue(res, row, 5);
    const char *replace_dst_str = PQgetvalue(res, row, 6);

    // 基本字段有效性检查
    CHECK_ERR_GOTO(!key_str || !name_str || !value_str, CFG_LOG_TAG, cleanup, "Missing required field values at row %zu", row);

    uint64_t type, attrb;
    CHECK_ERR_GOTO(!cfg_str_to_uint64(type_str, &type) || !cfg_str_to_uint64(attrb_str, &attrb), CFG_LOG_TAG, cleanup,
                      "Failed to parse 'type' or 'attrb' field at row %zu", row);

    shared_key = sdsnew(key_str);
    shared_name = sdsnew(name_str);
    CHECK_ERR_GOTO(!shared_key || !shared_name, CFG_LOG_TAG, cleanup, "Failed to allocate memory for shared key/name");

    if (replace_src_str && *replace_src_str && replace_dst_str) {
        replacements_str = cfg_replace_str(value_str, replace_src_str, replace_dst_str);
    } else {
        replacements_str = sdsnew(value_str);
    }
    CHECK_ERR_GOTO(!replacements_str, CFG_LOG_TAG, cleanup, "Failed to prepare value string");

    split_values = sdssplitlen(replacements_str, sdslen(replacements_str), COMMON_SEPARATOR, 1, &split_count);
    CHECK_ERR_GOTO(!split_values, CFG_LOG_TAG, cleanup, "Failed to split value string: %s", replacements_str);

    if (split_count == 0) {
        // 如果分割后为空，这不是一个致命错误，只是此行没有生成任何规则。
        // 我们可以认为它处理“成功”了。
        ret_status = CFG_SUCCESS;
        goto cleanup;
    }

    for (size_t i = 0; i < split_count; i++) {
        if (split_values[i] == NULL || sdslen(split_values[i]) == 0) {
            continue;
        }

        hs_item_t *item = (hs_item_t *)calloc(1, sizeof(hs_item_t));
        CHECK_ERR_GOTO(item == NULL, CFG_LOG_TAG, cleanup, "calloc failed for sub_item");

        item->key = sdsdup(shared_key);
        item->name = sdsdup(shared_name);
        item->value = sdsnew(split_values[i]);
        item->type = type;
        item->attrb = attrb;

        CHECK_ERR_GOTO(!item->key || !item->name || !item->value, CFG_LOG_TAG, cleanup, "Failed to allocate memory for sub_item fields");

        cvector_push_back(*items, item);
    }

    ret_status = CFG_SUCCESS; // 所有操作成功

cleanup:
    // --- 6. 统一的资源清理 ---
    // 释放所有在函数作用域内分配的、且未转移所有权的资源
    if (replacements_str) {
        sdsfree(replacements_str);
    }
    if (split_values) {
        sdsfreesplitres(split_values, split_count);
    }
    if (shared_key) {
        sdsfree(shared_key);
    }
    if (shared_name) {
        sdsfree(shared_name);
    }

    return ret_status;
}

static int regu_tdata_post_process(void *arg)
{
    hs_tdata_t *tdata = (hs_tdata_t *)arg;

    CHECK_ERR_EXEC(!hs_database_create(tdata, regu_thread_num), CFG_LOG_TAG, return CFG_FAILURE, "Failed to create Hyperscan database");

    return CFG_SUCCESS;
}

static const cfg_module_create_t module_desc = {
    .expect_cols = API_REGU_COL_NUM,
    .table_name = API_REGU_TABLE,
    .redis_key = API_REDIS_KEY_REGU,
    .tdata_size = sizeof(hs_tdata_t),
    .item_size = sizeof(hs_item_t),
    .create_item = regu_item_create,
    .destroy_item = regu_item_destroy,
    .post_process = regu_tdata_post_process, // 给base_tdata_t的ctx赋值
    .destroy_tdata = regu_tdata_destroy,
};

/**
 * @brief 创建regu模块的tdata对象
 *
 * @param user 用户参数（未使用）
 * @return void* 成功返回hs_tdata_t指针，失败返回NULL
 */
static void *regu_tdata_create(void *user)
{
    (void)user; // 参数未使用，防止编译器警告

    // 直接通过cfg_module_create分配和初始化tdata
    void *result = cfg_module_create((void *)&module_desc);
    if (!result) {
        return NULL;
    }

    return result;
}

config_plugin_t regu_plugin = {
    .name = "regu",
    .check_interval = 1,
    .grace_period = 3,
    .create = regu_tdata_create,
    .destroy = regu_tdata_destroy,
};

int regu_config_init(int thread_num)
{
    regu_thread_num = thread_num;
    regu_plugin_id = an_cfg_plugin_register(&regu_plugin);
    return regu_plugin_id;
}

int get_regu_plugin_id()
{
    return regu_plugin_id;
}
