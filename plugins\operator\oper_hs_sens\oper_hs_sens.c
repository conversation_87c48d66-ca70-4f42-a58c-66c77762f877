#include <an_calc.h>
#include <hs_match.h>
#include <hs_create.h>
#include <an_cfg_svc.h>
#include <db_proc_regu.h>

#define HS_SENS_LOG_TAG "oper_hs_sens"

// 输入输出类型枚举
typedef enum {
    HS_TYPE_URL = 0,
    HS_TYPE_BODY,
    HS_TYPE_COOKIES,
    HS_TYPE_HEADER,
    HS_TYPE_MAX
} hs_input_type_t;

// 统一的输入输出配置
typedef struct {
    const char *input_name;
    const char *output_name;
} hs_field_config_t;

// 上下文结构体
typedef struct {
    int input_indices[HS_TYPE_MAX];
    int output_indices[HS_TYPE_MAX];
    hs_result_t results[HS_TYPE_MAX];
} hs_sens_ctx_t;

// 字段配置表
static const hs_field_config_t g_field_configs[HS_TYPE_MAX] = {
    [HS_TYPE_URL] = {"hs_url", "hs_url_result"},
    [HS_TYPE_BODY] = {"hs_body", "hs_body_result"},
    [HS_TYPE_COOKIES] = {"hs_cookies", "hs_cookies_result"},
    [HS_TYPE_HEADER] = {"hs_header", "hs_header_result"}
};

// 依赖和输出变量定义
static const an_operator_variable_req_t hs_sens_dependencies[] = {
    {"hs_url",    AN_VAR_TYPE_STRING},
    {"hs_body",   AN_VAR_TYPE_STRING},
    {"hs_cookies", AN_VAR_TYPE_STRING},
    {"hs_header", AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN}
};

static const an_operator_variable_req_t hs_sens_outputs[] = {
    {"hs_url_result",    AN_VAR_TYPE_POINTER},
    {"hs_body_result",   AN_VAR_TYPE_POINTER},
    {"hs_cookies_result", AN_VAR_TYPE_POINTER},
    {"hs_header_result", AN_VAR_TYPE_POINTER},
    {NULL, AN_VAR_TYPE_UNKNOWN}
};

static const an_operator_variable_req_t *hs_get_deps(void)
{
    return hs_sens_dependencies;
}

static const an_operator_variable_req_t *hs_get_outs(void)
{
    return hs_sens_outputs;
}

/**
 * @brief 清理单个结果
 */
static void cleanup_single_result(hs_result_t *result)
{
    if (!result) return;

    if (result->match_items) {
        cvector_free(result->match_items);
        result->match_items = NULL;
    }

    result->data = NULL;
    result->data_len = -1;
    result->hs_items = NULL;
}

/**
 * @brief 初始化单个结果
 */
static void init_single_result(hs_result_t *result, const char *data, items_t hs_items)
{
    if (!result || !data) return;

    result->data = data;
    result->data_len = strlen(data);
    result->hs_items = hs_items;
    result->match_items = NULL;
    cvector_init(result->match_items, 0, hs_match_item_free);
}

/**
 * @brief 执行单个字段的扫描
 */
static bool scan_single_field(hs_result_t *result, hs_database_t *database, hs_scratch_t *scratch)
{
    if (!result || !database || !scratch || !result->data) {
        return false;
    }

    hs_error_t ret = hs_scan(database, result->data, result->data_len, 0, scratch, hs_match_cb, result);

    if (ret != HS_SUCCESS) {
        LOG_ERROR(HS_SENS_LOG_TAG, "hs_scan failed, error: %s", hs_error_info(ret));
        return false;
    }

    return true;
}

/**
 * @brief 重置函数 - 清理所有匹配结果
 */
static void hs_sens_reset(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    hs_sens_ctx_t *ctx = (hs_sens_ctx_t *)oper_ctx->user;

    for (int i = 0; i < HS_TYPE_MAX; i++) {
        cleanup_single_result(&ctx->results[i]);
    }
}

/**
 * @brief 执行函数 - 主要业务逻辑
 */
static double hs_sens_execute(an_calc_session_t *session, an_calc_oper_ctx_t *oper_ctx)
{
    CHECK_ERR_EXEC(!session || !oper_ctx || !oper_ctx->user || oper_ctx->thread_id < 0, HS_SENS_LOG_TAG, return 0.0, "hs_sens ctx err: %p", oper_ctx->user);

    hs_sens_ctx_t *ctx = (hs_sens_ctx_t *)oper_ctx->user;

    LOG_DEBUG(HS_SENS_LOG_TAG, "hs_sens_execute: %p", ctx);

    // 获取所有输入数据
    char *inputs[HS_TYPE_MAX];
    bool has_input = false;

    for (int i = 0; i < HS_TYPE_MAX; i++) {
        inputs[i] = (ctx->input_indices[i] >= 0) ? an_calc_get_string(session, ctx->input_indices[i]) : NULL;
        if (inputs[i]) {
            has_input = true;
        }
    }
    CHECK_ERR_EXEC(!has_input, HS_SENS_LOG_TAG, return 0.0, "All inputs are NULL: %p: %d, %d, %d, %d", oper_ctx->user, inputs[0], inputs[1], inputs[2], inputs[3]);

    // 获取配置和数据库
    int plugin_id = get_regu_plugin_id();
    CHECK_ERR_EXEC(plugin_id < 0, HS_SENS_LOG_TAG, return 0.0, "Invalid plugin ID: %d", plugin_id);

    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(plugin_id);
    CHECK_ERR_EXEC(!cfg_wrap || !cfg_wrap->config, HS_SENS_LOG_TAG, return 0.0, "Failed to get config wrapper for regu plugin");

    hs_tdata_t *tdata = (hs_tdata_t *)cfg_wrap->config;
    hs_db_t *db = (hs_db_t *)tdata->ctx;

    if (!db || !db->database || !db->scratchs[oper_ctx->thread_id]) {
        LOG_ERROR(HS_SENS_LOG_TAG, "Failed to get hyperscan database");
        an_cfg_wrapper_put(plugin_id, cfg_wrap);
        return 0.0;
    }

    // 处理每个输入字段
    bool scan_success = true;
    for (int i = 0; i < HS_TYPE_MAX; i++) {
        if (inputs[i]) {
            init_single_result(&ctx->results[i], inputs[i], tdata->items);

            if (!scan_single_field(&ctx->results[i], db->database,
                db->scratchs[oper_ctx->thread_id])) {
                scan_success = false;
                break;
            }
        }
    }

    an_cfg_wrapper_put(plugin_id, cfg_wrap);

    if (!scan_success) {
        return 0.0;
    }

    // 设置输出结果
    for (int i = 0; i < HS_TYPE_MAX; i++) {
        if (ctx->output_indices[i] >= 0) {
            an_calc_set_pointer(session, ctx->output_indices[i], &ctx->results[i]);
        }
    }

    return 100.0;
}

/**
 * @brief 初始化函数
 */
static int hs_sens_init(an_calc_oper_ctx_t *oper_ctx)
{
    LOG_INFO(HS_SENS_LOG_TAG, "Initializing oper_hs_sens");

    hs_sens_ctx_t *ctx = (hs_sens_ctx_t *)calloc(1, sizeof(hs_sens_ctx_t));
    if (!ctx) {
        LOG_ERROR(HS_SENS_LOG_TAG, "Failed to allocate memory for context");
        return -1;
    }

    // 初始化索引为无效值
    for (int i = 0; i < HS_TYPE_MAX; i++) {
        ctx->input_indices[i] = -1;
        ctx->output_indices[i] = -1;
    }

    // 解析依赖和输出索引
    bool has_valid_input = false;
    bool has_valid_output = false;

    for (int i = 0; i < HS_TYPE_MAX; i++) {
        ctx->input_indices[i] = oper_ctx->get_depend_index(oper_ctx, g_field_configs[i].input_name);
        ctx->output_indices[i] = oper_ctx->get_output_index(oper_ctx, g_field_configs[i].output_name);

        if (ctx->input_indices[i] >= 0) has_valid_input = true;
        if (ctx->output_indices[i] >= 0) has_valid_output = true;
    }

    if (!has_valid_input) {
        LOG_WARN(HS_SENS_LOG_TAG, "No valid input dependencies found");
    }

    if (!has_valid_output) {
        LOG_WARN(HS_SENS_LOG_TAG, "No valid output variables bound");
    }

    for (int i = 0; i < HS_TYPE_MAX; i++) {
        LOG_INFO(HS_SENS_LOG_TAG, "Successfully oper_hs_sens: %p: input[%d]: %d, output[%d]: %d",
                                ctx, i, ctx->input_indices[i], i, ctx->output_indices[i]);
    }

    oper_ctx->user = ctx;

    return 0;
}

/**
 * @brief 清理函数
 */
static void hs_sens_cleanup(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    // 清理所有结果
    hs_sens_reset(oper_ctx);

    // 释放上下文
    free(oper_ctx->user);
    oper_ctx->user = NULL;

    LOG_INFO(HS_SENS_LOG_TAG, "oper_hs_sens cleaned up");
}

// 算子插件定义
an_operator_plugin_t oper_hs_sens = {
    .name = "oper_hs_sens",
    .version = "1.0.0",
    .get_dependencies = hs_get_deps,
    .get_outputs = hs_get_outs,
    .init = hs_sens_init,
    .fini = hs_sens_cleanup,
    .execute = hs_sens_execute,
    .reset = hs_sens_reset
};
