#include "khash.h"
#include "util_ip.h"
#include "db_proc_white.h"

static int white_plugin_id = -1;

/**
 * @brief 清理白名单数据库资源
 * @param db 白名单数据库结构
 */
static void cleanup_white_db(white_db_t *db)
{
    if (!db) {
        return;
    }

    if (db->api_hash) {
        kh_destroy(api_hash, db->api_hash);
        db->api_hash = NULL;
    }

    if (db->app_hash) {
        kh_destroy(app_hash, db->app_hash);
        db->app_hash = NULL;
    }

    if (db->ip_tree) {
        util_free_ip_tree(db->ip_tree);
        db->ip_tree = NULL;
    }

    free(db);
}

/**
 * @brief 销毁白名单tdata及其所有资源
 * @param arg 指向white_tdata_t的指针
 * @return int CFG_SUCCESS表示成功
 */
static int white_tdata_destroy(const void *arg)
{
    if (!arg) {
        LOG_WARN(CFG_LOG_TAG, "Attempt to destroy NULL white tdata");
        return CFG_SUCCESS;
    }

    white_tdata_t *tdata = (white_tdata_t *)arg;

    // 清理所有items（cvector_free会自动调用destroy回调）
    if (tdata->items) {
        cvector_free(tdata->items);
        tdata->items = NULL;
    }

    // 清理数据库资源
    cleanup_white_db((white_db_t *)tdata->ctx);
    tdata->ctx = NULL;

    free(tdata);
    LOG_INFO(CFG_LOG_TAG, "White tdata destroyed successfully");
    return CFG_SUCCESS;
}

/**
 * @brief 销毁单个白名单项及其资源
 * @param arg 指向white_item_t的指针
 */
static void white_item_destroy(void *arg)
{
    if (!arg) {
        return;
    }

    white_item_t *item = (white_item_t *)arg;

    // 释放sds字符串
    if (item->key) {
        sdsfree(item->key);
        item->key = NULL;
    }

    if (item->value) {
        sdsfree(item->value);
        item->value = NULL;
    }

    // 重置类型
    item->type = -1;

    free(item);
}

/**
 * @brief 验证白名单类型和对应数据的有效性
 * @param white_type 白名单类型
 * @param ip_list IP列表
 * @param applist 应用列表
 * @param apilist API列表
 * @return const char* 返回对应的数据列表，无效返回NULL
 */
static const char *get_list_by_type(int white_type, const char *ip_list, const char *applist, const char *apilist)
{
    switch (white_type) {
    case 1: // IP类型
        return ip_list;
    case 2: // APP类型
        return applist;
    case 3: // API类型
        return apilist;
    default:
        return NULL;
    }
}

/**
 * @brief 创建单个白名单项
 * @param key 键值
 * @param white_type 白名单类型
 * @param value 值
 * @return white_item_t* 成功返回item指针，失败返回NULL
 */
static white_item_t *create_single_item(const char *key, int white_type, const char *value)
{
    white_item_t *item = (white_item_t *)calloc(1, sizeof(white_item_t));
    if (!item) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to allocate memory for white_item_t");
        return NULL;
    }

    item->type = white_type;
    item->key = sdsnew(key);
    item->value = sdsnew(value);

    if (!item->key || !item->value) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to create sds strings for item");
        white_item_destroy(item);
        return NULL;
    }

    return item;
}

/**
 * @brief 清理已创建的items（用于错误恢复）
 * @param items items向量
 * @param start_idx 开始清理的索引
 */
static void cleanup_created_items(items_t *items, size_t start_idx)
{
    if (!items || !*items) {
        return;
    }

    size_t current_size = cvector_size(*items);
    for (size_t i = start_idx; i < current_size; i++) {
        white_item_destroy((*items)[i]);
    }

    // TODO:调整向量大小，提高性能
    // cvector_resize(*items, start_idx);
}

/**
 * @brief 从数据库结果创建白名单项
 * @param items 输出的items向量
 * @param res 数据库查询结果
 * @param row 当前行号
 * @return int CFG_SUCCESS表示成功，CFG_FAILURE表示失败
 */
static int white_item_create(items_t *items, PGresult *res, size_t row)
{
    // 获取数据库字段值
    const char *key = safe_get_value(res, row, 0);
    int white_type = cfg_str_to_int(safe_get_value(res, row, 1));
    const char *ip_list = safe_get_value(res, row, 2);
    const char *applist = safe_get_value(res, row, 3);
    const char *apilist = safe_get_value(res, row, 4);

    // 验证基本参数
    if (!key || strlen(key) == 0) {
        LOG_WARN(CFG_LOG_TAG, "Invalid key in white list row %zu", row);
        return CFG_FAILURE;
    }

    // 根据类型获取对应的列表数据
    const char *target_list = get_list_by_type(white_type, ip_list, applist, apilist);
    if (!target_list || strlen(target_list) == 0) {
        LOG_WARN(CFG_LOG_TAG, "No valid data for white type %d in row %zu", white_type, row);
        return CFG_FAILURE;
    }

    // 分割列表数据
    int rslt_cnt = 0;
    sds *rslt = sdssplitlen(target_list, strlen(target_list), COMMON_SEPARATOR, 1, &rslt_cnt);
    if (!rslt || rslt_cnt == 0) {
        LOG_WARN(CFG_LOG_TAG, "Failed to split list data for row %zu", row);
        if (rslt) {
            sdsfreesplitres(rslt, rslt_cnt);
        }
        return CFG_FAILURE;
    }

    // 记录开始位置，用于错误恢复
    size_t start_idx = cvector_size(*items);
    bool success = true;

    // 为每个分割出的值创建白名单项
    for (int i = 0; i < rslt_cnt; i++) {
        // 跳过空值
        if (!rslt[i] || sdslen(rslt[i]) == 0) {
            continue;
        }

        white_item_t *item = create_single_item(key, white_type, rslt[i]);
        if (!item) {
            LOG_ERROR(CFG_LOG_TAG, "Failed to create white item for value '%s'", rslt[i]);
            success = false;
            break;
        }

        cvector_push_back(*items, item);
    }

    // 清理分割结果
    sdsfreesplitres(rslt, rslt_cnt);

    // 错误处理：如果创建失败，清理已创建的items
    if (!success) {
        cleanup_created_items(items, start_idx);
        return CFG_FAILURE;
    }

    size_t created_count = cvector_size(*items) - start_idx;
    LOG_DEBUG(CFG_LOG_TAG, "Created %zu white items for key '%s', type %d",
        created_count, key, white_type);

    return CFG_SUCCESS;
}

/**
 * @brief 初始化白名单数据库结构
 * @return white_db_t* 成功返回数据库指针，失败返回NULL
 */
static white_db_t *init_white_db(void)
{
    white_db_t *db = (white_db_t *)calloc(1, sizeof(white_db_t));
    if (!db) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to allocate memory for white_db_t");
        return NULL;
    }

    // 初始化API哈希表
    db->api_hash = kh_init(api_hash);
    if (!db->api_hash) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to initialize API hash table");
        free(db);
        return NULL;
    }

    // 初始化APP哈希表
    db->app_hash = kh_init(app_hash);
    if (!db->app_hash) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to initialize APP hash table");
        kh_destroy(api_hash, db->api_hash);
        free(db);
        return NULL;
    }

    // 初始化IP树
#ifdef RADIX_IP
    db->ip_tree = radix_tree_create();
#else
    db->ip_tree = ip2lookup_create();
#endif
    if (!db->ip_tree) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to initialize IP tree");
        kh_destroy(api_hash, db->api_hash);
        kh_destroy(app_hash, db->app_hash);
        free(db);
        return NULL;
    }

    return db;
}

/**
 * @brief 处理IP类型的白名单项
 * @param item 白名单项
 * @param ip_tree IP树结构
 * @return bool 成功返回true
 */
static bool process_ip_item(const white_item_t *item, void *ip_tree)
{
    if (!item || !item->value || !ip_tree) {
        return false;
    }

    util_add_ip_tree(item->value, ip_tree);

    return true;
}

/**
 * @brief 处理哈希表类型的白名单项（APP/API）
 * @param item 白名单项
 * @param hash_table 哈希表
 * @param hash_type 哈希表类型名称（用于日志）
 * @return bool 成功返回true
 */
static bool process_hash_item(const white_item_t *item, void *hash_table, const char *hash_type)
{
    if (!item || !item->value || !hash_table) {
        return false;
    }

    khash_t(api_hash) *hash = (khash_t(api_hash) *)hash_table;

    // 检查是否已存在
    khiter_t k = kh_get(api_hash, hash, item->value);
    if (k != kh_end(hash)) {
        LOG_DEBUG(CFG_LOG_TAG, "%s value '%s' already exists, skipping", hash_type, item->value);
        return true; // 不算错误，只是跳过
    }

    // 插入新值
    int ret;
    k = kh_put(api_hash, hash, item->value, &ret);
    if (ret <= 0) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to insert %s value '%s' into hash table", hash_type, item->value);
        return false;
    }

    kh_val(hash, k) = item->key;
    return true;
}

/**
 * @brief 处理单个白名单项，根据类型添加到相应的数据结构
 * @param item 白名单项
 * @param db 白名单数据库
 * @return bool 成功返回true
 */
static bool process_white_item(const white_item_t *item, white_db_t *db)
{
    if (!item || !db) {
        return false;
    }

    switch (item->type) {
    case 1: // IP类型
        return process_ip_item(item, db->ip_tree);

    case 2: // APP类型
        return process_hash_item(item, db->app_hash, "APP");

    case 3: // API类型
        return process_hash_item(item, db->api_hash, "API");

    default:
        LOG_WARN(CFG_LOG_TAG, "Unknown white item type: %d", item->type);
        return false;
    }
}

/**
 * @brief 白名单数据后处理，构建索引结构
 * @param arg 指向white_tdata_t的指针
 * @return int CFG_SUCCESS表示成功，CFG_FAILURE表示失败
 */
static int white_tdata_post_process(void *arg)
{
    if (!arg) {
        LOG_ERROR(CFG_LOG_TAG, "Invalid tdata parameter");
        return CFG_FAILURE;
    }

    white_tdata_t *tdata = (white_tdata_t *)arg;
    if (!tdata->items) {
        LOG_WARN(CFG_LOG_TAG, "No items to process, creating empty database");
        tdata->ctx = NULL;
        return CFG_SUCCESS;
    }

    // 初始化数据库结构
    white_db_t *db = init_white_db();
    if (!db) {
        LOG_ERROR(CFG_LOG_TAG, "Failed to initialize white database");
        return CFG_FAILURE;
    }

    // 处理统计
    size_t items_count = cvector_size(tdata->items);
    size_t processed_count = 0;
    size_t failed_count = 0;
    size_t ip_count = 0, app_count = 0, api_count = 0;

    // 处理所有白名单项
    for (size_t i = 0; i < items_count; i++) {
        white_item_t *item = (white_item_t *)tdata->items[i];

        if (!item || !item->value) {
            LOG_WARN(CFG_LOG_TAG, "Invalid item at index %zu", i);
            failed_count++;
            continue;
        }

        if (process_white_item(item, db)) {
            processed_count++;

            // 统计各类型数量
            switch (item->type) {
            case 1: ip_count++; break;
            case 2: app_count++; break;
            case 3: api_count++; break;
            }
        } else {
            LOG_WARN(CFG_LOG_TAG, "Failed to process item at index %zu (type: %d, value: '%s')",
                i, item->type, item->value);
            failed_count++;
        }
    }

    // 设置数据库上下文
    tdata->ctx = db;

    LOG_INFO(CFG_LOG_TAG, "White list post-process completed: %zu total, %zu processed, %zu failed",
        items_count, processed_count, failed_count);
    LOG_INFO(CFG_LOG_TAG, "White list statistics: %zu IPs, %zu APPs, %zu APIs",
        ip_count, app_count, api_count);

    return CFG_SUCCESS;
}

/**
 * @brief 白名单模块配置描述
 */
static const cfg_module_create_t module_desc = {
    .expect_cols = API_WHITE_COL_NUM,
    .table_name = API_WHITE_TABLE,
    .redis_key = API_REDIS_KEY_WHITE,
    .tdata_size = sizeof(white_tdata_t),
    .item_size = sizeof(white_item_t),
    .create_item = white_item_create,
    .destroy_item = white_item_destroy,
    .post_process = white_tdata_post_process,
    .destroy_tdata = white_tdata_destroy,
};

/**
 * @brief 创建白名单tdata
 * @param arg 参数（未使用）
 * @return void* 成功返回tdata指针，失败返回NULL
 */
static void *white_tdata_create(void *arg)
{
    return cfg_module_create((void *)&module_desc);
}

/**
 * @brief 白名单插件配置
 */
static config_plugin_t white_plugin = {
    .name = "white",
    .check_interval = 1,
    .grace_period = 3,
    .create = white_tdata_create,
    .destroy = white_tdata_destroy,
};

/**
 * @brief 初始化白名单配置插件
 * @return int 成功返回插件ID，失败返回负数
 */
int white_config_init(void)
{
    white_plugin_id = an_cfg_plugin_register(&white_plugin);

    if (white_plugin_id >= 0) {
        LOG_INFO(CFG_LOG_TAG, "White config plugin initialized successfully with ID: %d", white_plugin_id);
    } else {
        LOG_ERROR(CFG_LOG_TAG, "Failed to initialize white config plugin");
    }

    return white_plugin_id;
}

/**
 * @brief 获取白名单插件ID
 * @return int 插件ID
 */
int get_white_plugin_id(void)
{
    return white_plugin_id;
}
