#include "vars_manager.h"
#include "util_log.h"
#include <stdlib.h>
#include <string.h>

#define VAR_MGR_LOG_TAG "vars_manager"

// 全局配置管理器
static vars_manager_t *g_var_mgr = NULL;

// 基础变量配置表
static const struct {
    const char *name;
    an_calc_variable_type_t type;
} g_base_var_configs[] = {
    {"sip", AN_VAR_TYPE_STRING},
    {"dip", AN_VAR_TYPE_STRING},
    {"url", AN_VAR_TYPE_STRING},
    {"domain", AN_VAR_TYPE_STRING},
    {"body", AN_VAR_TYPE_STRING},
    {"cookies", AN_VAR_TYPE_STRING},
    {"header", AN_VAR_TYPE_STRING},
    {"sip_geo", AN_VAR_TYPE_STRING},
    {"dip_geo", AN_VAR_TYPE_STRING},
    {"bs_result", AN_VAR_TYPE_POINTER},
    {"hs_url_result", AN_VAR_TYPE_POINTER},
    {"hs_body_result", AN_VAR_TYPE_POINTER},
    {"hs_cookies_result", AN_VAR_TYPE_POINTER},
    {"hs_header_result", AN_VAR_TYPE_POINTER},
    {NULL, AN_VAR_TYPE_UNKNOWN}
};

// 字段映射配置表
static const struct {
    const char *field_name; // 字段名
    const char *result_var; // 对应的结果变量
} g_field_map_configs[] = {
    {"url", "hs_url_result"},
    {"body", "hs_body_result"},
    {"cookies", "hs_cookies_result"},
    {"header", "hs_header_result"},
    {"all", "hs_url_result/hs_body_result/hs_cookies_result/hs_header_result"},
    {NULL, NULL}
};

// 预定义算子配置结构
typedef struct {
    const char *instance_name;
    const char *operator_name;
    size_t binding_count;
    const an_operator_binding_t *bindings;  // 指向绑定数组的指针
} predefined_operator_config_t;

// 预定义绑定配置数组
static const an_operator_binding_t g_sip_geo_bindings[] = {
    {"ip", "sip"},
    {"ip_geo", "sip_geo"}
};

static const an_operator_binding_t g_dip_geo_bindings[] = {
    {"ip", "dip"},
    {"ip_geo", "dip_geo"}
};

static const an_operator_binding_t g_bs_bindings[] = {
    {"bs_input_sip", "sip"},
    {"bs_input_domain", "domain"},
    {"bs_result", "bs_result"}
};

static const an_operator_binding_t g_hs_sens_bindings[] = {
    {"hs_url", "url"},
    {"hs_body", "body"},
    {"hs_cookies", "cookies"},
    {"hs_header", "header"},
    {"hs_url_result", "hs_url_result"},
    {"hs_body_result", "hs_body_result"},
    {"hs_cookies_result", "hs_cookies_result"},
    {"hs_header_result", "hs_header_result"}
};

static const predefined_operator_config_t g_predefined_operator_configs[] = {
    {"op_sip_geo", "oper_ip_geo", 2, g_sip_geo_bindings },
    {"op_dip_geo", "oper_ip_geo", 2, g_dip_geo_bindings},
    {"op_bs", "oper_business", 3, g_bs_bindings},
    {"op_hs_sens_count", "oper_hs_sens", 8, g_hs_sens_bindings},
    {NULL, NULL, 0, NULL}
};

/**
 * @brief 创建变量配置
 */
static var_config_t *create_var_config(const char *name, an_calc_variable_type_t type, var_category_t category)
{
    if (!name) return NULL;

    var_config_t *config = (var_config_t *)calloc(1, sizeof(var_config_t));
    if (!config) return NULL;

    config->name = sdsnew(name);
    config->type = type;
    config->category = category;
    config->index = -1;
    config->is_registered = false;

    if (!config->name) {
        free(config);
        return NULL;
    }

    return config;
}

/**
 * @brief 销毁变量配置
 */
static void destroy_var_config(var_config_t *config)
{
    if (!config) return;

    config->is_registered = false;
    config->index = -1;
    config->category = VAR_CATEGORY_ERROR;
    config->type = AN_VAR_TYPE_UNKNOWN;
    sdsfree(config->name);

    free(config);
}

/**
 * @brief 创建字段映射
 */
static field_mapping_t *create_field_mapping(const char *field_name, const char *result_var)
{
    if (!field_name || !result_var) return NULL;

    field_mapping_t *mapping = (field_mapping_t *)calloc(1, sizeof(field_mapping_t));
    if (!mapping) return NULL;

    mapping->field_name = sdsnew(field_name);
    mapping->result_var = sdsnew(result_var);

    if (!mapping->field_name || !mapping->result_var) {
        sdsfree(mapping->field_name);
        sdsfree(mapping->result_var);
        free(mapping);
        return NULL;
    }

    return mapping;
}

/**
 * @brief 销毁字段映射
 */
static void destroy_field_mapping(field_mapping_t *mapping)
{
    if (!mapping) return;

    sdsfree(mapping->field_name);
    sdsfree(mapping->result_var);

    free(mapping);
}

/**
 * @brief 创建算子配置
 */
static operator_config_t *create_operator_config(const char *instance_name, const char *operator_name, const an_operator_binding_t *bindings, size_t binding_count)
{
    CHECK_ERR_EXEC(!instance_name || !operator_name || !bindings, VAR_MGR_LOG_TAG, return NULL, "Invalid input parameters");

    operator_config_t *config = (operator_config_t *)calloc(1, sizeof(operator_config_t));
    CHECK_ERR_EXEC(!config, VAR_MGR_LOG_TAG, return NULL, "Failed to allocate operator config");

    config->instance_name = sdsnew(instance_name);
    config->operator_name = sdsnew(operator_name);
    config->binding_count = binding_count;
    config->is_registered = false;

    if (!config->instance_name || !config->operator_name) {
        sdsfree(config->instance_name);
        sdsfree(config->operator_name);
        free(config);
        return NULL;
    }

    // 复制绑定配置
    config->bindings = (an_operator_binding_t *)calloc(binding_count, sizeof(an_operator_binding_t));
    if (!config->bindings) {
        sdsfree(config->instance_name);
        sdsfree(config->operator_name);
        free(config);
        return NULL;
    }

    for (size_t i = 0; i < binding_count; i++) {
        config->bindings[i].logical_name = strdup(bindings[i].logical_name);
        config->bindings[i].target_variable = strdup(bindings[i].target_variable);

        if (!config->bindings[i].logical_name || !config->bindings[i].target_variable) {
            // 清理已分配的内存
            for (size_t j = 0; j <= i; j++) {
                free((char *)config->bindings[j].logical_name);
                free((char *)config->bindings[j].target_variable);
            }
            free(config->bindings);
            sdsfree(config->instance_name);
            sdsfree(config->operator_name);
            free(config);
            return NULL;
        }
    }

    return config;
}

/**
 * @brief 销毁算子配置
 */
static void destroy_operator_config(operator_config_t *config)
{
    if (!config) return;

    sdsfree(config->instance_name);
    sdsfree(config->operator_name);

    if (config->bindings) {
        for (size_t i = 0; i < config->binding_count; i++) {
            free((char *)config->bindings[i].logical_name);
            free((char *)config->bindings[i].target_variable);
        }
        free(config->bindings);
    }

    free(config);
}

/**
 * @brief 初始化配置管理器
 */
int vars_manager_init(void)
{
    if (g_var_mgr) {
        vars_manager_destroy();
    }

    g_var_mgr = (vars_manager_t *)calloc(1, sizeof(vars_manager_t));
    CHECK_ERR_EXEC(!g_var_mgr, VAR_MGR_LOG_TAG, return -1, "Failed to allocate config manager");

    // 初始化哈希表
    g_var_mgr->var_configs = kh_init(str);
    g_var_mgr->field_mappings = kh_init(str);
    g_var_mgr->operator_configs = kh_init(str);

    if (!g_var_mgr->var_configs || !g_var_mgr->field_mappings || !g_var_mgr->operator_configs) {
        LOG_ERROR(VAR_MGR_LOG_TAG, "Failed to initialize hash tables");
        vars_manager_destroy();
        return -1;
    }

    // 初始化基础变量配置
    for (int i = 0; g_base_var_configs[i].name; i++) {
        if (add_variable_config(g_base_var_configs[i].name, g_base_var_configs[i].type, VAR_CATEGORY_BASE) != 0) {
            LOG_ERROR(VAR_MGR_LOG_TAG, "Failed to add base variable config: %s", g_base_var_configs[i].name);
        }
    }

    // 初始化字段映射配置
    for (int i = 0; g_field_map_configs[i].field_name; i++) {
        field_mapping_t *mapping = create_field_mapping(g_field_map_configs[i].field_name, g_field_map_configs[i].result_var);
        CHECK_ERR_EXEC(!mapping, VAR_MGR_LOG_TAG, return -1, "Failed to create field mapping: %s", g_field_map_configs[i].field_name);

        int ret;
        khiter_t k = kh_put(str, g_var_mgr->field_mappings, mapping->field_name, &ret);
        if (ret >= 0) {
            kh_value(g_var_mgr->field_mappings, k) = mapping;
        } else {
            destroy_field_mapping(mapping);
        }
    }

    // 初始化预定义算子配置
    for (int i = 0; g_predefined_operator_configs[i].instance_name; i++) {
        const predefined_operator_config_t *op_cfg = &g_predefined_operator_configs[i];

        // 创建绑定数组
        an_operator_binding_t *bindings = (an_operator_binding_t *)calloc(op_cfg->binding_count, sizeof(an_operator_binding_t));
        if (!bindings) continue;

        for (size_t j = 0; j < op_cfg->binding_count; j++) {
            bindings[j].logical_name = strdup(op_cfg->bindings[j].logical_name);
            bindings[j].target_variable = strdup(op_cfg->bindings[j].target_variable);
        }

        operator_config_t *config = create_operator_config(op_cfg->instance_name, op_cfg->operator_name, bindings, op_cfg->binding_count);

        // 清理临时绑定数组
        for (size_t j = 0; j < op_cfg->binding_count; j++) {
            free((char *)bindings[j].logical_name);
            free((char *)bindings[j].target_variable);
        }
        free(bindings);

        if (!config) {
            LOG_WARN(VAR_MGR_LOG_TAG, "Failed to create operator config: %s", op_cfg->instance_name);
            continue;
        }

        int ret;
        khiter_t k = kh_put(str, g_var_mgr->operator_configs, config->instance_name, &ret);
        if (ret >= 0) {
            kh_value(g_var_mgr->operator_configs, k) = config;
        } else {
            destroy_operator_config(config);
        }
    }

    LOG_INFO(VAR_MGR_LOG_TAG, "Config manager initialized successfully");
    return 0;
}

/**
 * @brief 销毁配置管理器
 */
void vars_manager_destroy(void)
{
    if (!g_var_mgr) return;

    // 清理变量配置
    if (g_var_mgr->var_configs) {
        khiter_t k;
        for (k = kh_begin(g_var_mgr->var_configs); k != kh_end(g_var_mgr->var_configs); ++k) {
            if (kh_exist(g_var_mgr->var_configs, k)) {
                destroy_var_config((var_config_t *)kh_value(g_var_mgr->var_configs, k));
            }
        }
        kh_destroy(str, g_var_mgr->var_configs);
    }

    // 清理字段映射
    if (g_var_mgr->field_mappings) {
        khiter_t k;
        for (k = kh_begin(g_var_mgr->field_mappings); k != kh_end(g_var_mgr->field_mappings); ++k) {
            if (kh_exist(g_var_mgr->field_mappings, k)) {
                destroy_field_mapping((field_mapping_t *)kh_value(g_var_mgr->field_mappings, k));
            }
        }
        kh_destroy(str, g_var_mgr->field_mappings);
    }

    // 清理算子配置
    if (g_var_mgr->operator_configs) {
        khiter_t k;
        for (k = kh_begin(g_var_mgr->operator_configs); k != kh_end(g_var_mgr->operator_configs); ++k) {
            if (kh_exist(g_var_mgr->operator_configs, k)) {
                destroy_operator_config((operator_config_t *)kh_value(g_var_mgr->operator_configs, k));
            }
        }
        kh_destroy(str, g_var_mgr->operator_configs);
    }

    // 清理动态变量列表
    if (g_var_mgr->dynamic_vars) {
        for (size_t i = 0; i < cvector_size(g_var_mgr->dynamic_vars); i++) {
            sdsfree(g_var_mgr->dynamic_vars[i]);
        }
        cvector_free(g_var_mgr->dynamic_vars);
    }

    free(g_var_mgr);
    g_var_mgr = NULL;

    LOG_INFO(VAR_MGR_LOG_TAG, "Config manager destroyed");
}

/**
 * @brief 获取配置管理器实例
 */
vars_manager_t *get_vars_manager(void)
{
    return g_var_mgr;
}

/**
 * @brief 根据字段名获取映射变量
 * @param field_name 字段名
 * @return 映射变量
 */
const char *get_field_mapping_var(const char *field_name)
{
    if (!g_var_mgr || !field_name) return NULL;

    khiter_t k = kh_get(str, g_var_mgr->field_mappings, field_name);
    if (k == kh_end(g_var_mgr->field_mappings)) {
        LOG_WARN(VAR_MGR_LOG_TAG, "Unknown field '%s', using default mapping", field_name);
        return "hs_body_result";  // 默认映射
    }

    field_mapping_t *mapping = (field_mapping_t *)kh_value(g_var_mgr->field_mappings, k);

    return mapping ? mapping->result_var : NULL;
}

/**
 * @brief 添加变量配置
 */
int add_variable_config(const char *name, an_calc_variable_type_t type, var_category_t category)
{
    if (!g_var_mgr || !name) return -1;

    // 检查是否已存在, 如果存在则返回0
    khiter_t k = kh_get(str, g_var_mgr->var_configs, name);
    CHECK_ERR_EXEC(k != kh_end(g_var_mgr->var_configs), VAR_MGR_LOG_TAG, return 0, "Variable config already exists: %s", name);

    // 若不存在则创建变量配置
    var_config_t *config = create_var_config(name, type, category);
    CHECK_ERR_EXEC(!config, VAR_MGR_LOG_TAG, return -1, "Failed to create variable config: %s", name);

    int ret;
    k = kh_put(str, g_var_mgr->var_configs, config->name, &ret);
    if (ret >= 0) { // 添加到哈希表成功
        kh_value(g_var_mgr->var_configs, k) = config;

        // 如果是动态变量，添加到动态变量列表
        if (category == VAR_CATEGORY_DYNAMIC || category == VAR_CATEGORY_OPERATOR) {
            cvector_push_back(g_var_mgr->dynamic_vars, sdsnew(name));
        }
        // LOG_DEBUG(VAR_MGR_LOG_TAG, "Added variable config: %s", name);
        return 0;
    } else { // 添加到哈希表失败
        destroy_var_config(config);
        LOG_ERROR(VAR_MGR_LOG_TAG, "Failed to add variable config to hash table: %s", name);
        return -1;
    }
}

/**
 * @brief 获取变量索引
 */
int get_variable_index(const char *var_name)
{
    if (!g_var_mgr || !var_name) return -1;

    khiter_t k = kh_get(str, g_var_mgr->var_configs, var_name);
    if (k == kh_end(g_var_mgr->var_configs)) {
        return -1;
    }

    var_config_t *config = (var_config_t *)kh_value(g_var_mgr->var_configs, k);
    return config ? config->index : -1;
}

/**
 * @brief 安全注册变量
 */
int safe_register_variable(an_calc_t *engine, const char *var_name, an_calc_variable_type_t type)
{
    CHECK_ERR_EXEC(!engine || !var_name || !g_var_mgr, VAR_MGR_LOG_TAG, return -1, "Invalid parameters for variable registration");

    khiter_t k = kh_get(str, g_var_mgr->var_configs, var_name);
    if (k != kh_end(g_var_mgr->var_configs)) { // 变量已存在
        var_config_t *config = (var_config_t *)kh_value(g_var_mgr->var_configs, k); // 获取变量配置
        if (config && config->is_registered) { // 变量已注册
            LOG_DEBUG(VAR_MGR_LOG_TAG, "Variable '%s' already registered, skipping", var_name);
            return 0;
        }
    }

    int ret = an_calc_reg_var(engine, var_name, type);
    CHECK_ERR_EXEC(ret == -1, VAR_MGR_LOG_TAG, return -1, "Failed to register variable '%s', error code: %d", var_name, ret);

    // 更新配置状态
    if (k != kh_end(g_var_mgr->var_configs)) {
        var_config_t *config = (var_config_t *)kh_value(g_var_mgr->var_configs, k);
        if (config) {
            config->is_registered = true;
        }
    }

    LOG_DEBUG(VAR_MGR_LOG_TAG, "Successfully registered variable: %s", var_name);
    return 0;
}

/**
 * @brief 安全注册算子实例
 *
 * @param engine 引擎实例
 * @param instance_name 算子实例名
 * @param plugin_name 算子插件名
 * @param bindings 绑定配置
 * @param binding_count 绑定数量
 * @return 0 on success, -1 on failure
 */
int safe_register_operator(an_calc_t *engine, const char *instance_name, const char *plugin_name, const an_operator_binding_t *bindings, size_t binding_count)
{
    CHECK_ERR_EXEC(!engine || !instance_name || !plugin_name || !g_var_mgr, VAR_MGR_LOG_TAG, return -1, "Invalid parameters for operator registration");

    khiter_t k = kh_get(str, g_var_mgr->operator_configs, instance_name);
    if (k != kh_end(g_var_mgr->operator_configs)) { // 算子实例已存在
        operator_config_t *config = (operator_config_t *)kh_value(g_var_mgr->operator_configs, k); // 获取算子实例配置
        if (config && config->is_registered) { // 算子实例已注册
            LOG_DEBUG(VAR_MGR_LOG_TAG, "Operator instance '%s' already registered, skipping", instance_name);
            return 0;
        }
    }

    bool ret = an_calc_reg_oper_ins(engine, instance_name, plugin_name, bindings, binding_count);
    CHECK_ERR_EXEC(!ret, VAR_MGR_LOG_TAG, return -1, "Failed to register operator instance: %s -> %s", instance_name, plugin_name);

    // 更新配置状态
    if (k != kh_end(g_var_mgr->operator_configs)) {
        operator_config_t *config = (operator_config_t *)kh_value(g_var_mgr->operator_configs, k);
        if (config) {
            config->is_registered = true;
        }
    }

    LOG_DEBUG(VAR_MGR_LOG_TAG, "Successfully registered operator instance: %s -> %s", instance_name, plugin_name);
    return 0;
}

/**
 * @brief 动态缓存所有变量索引
 */
void cache_all_variable_indices(workflow_ctx_t *ctx)
{
    // CHECK_DEBUG_EXEC(ctx && ctx->engine && g_var_mgr, VAR_MGR_LOG_TAG, return, "Invalid context for caching variable indices");

    int cached_count = 0;
    int failed_count = 0;

    // 缓存所有变量的索引
    khiter_t k;
    for (k = kh_begin(g_var_mgr->var_configs); k != kh_end(g_var_mgr->var_configs); ++k) {
        if (!kh_exist(g_var_mgr->var_configs, k)) {
            continue;
        }

        var_config_t *config = (var_config_t *)kh_value(g_var_mgr->var_configs, k);
        if (!config) continue;

        int index = an_calc_get_var_index(ctx->engine, config->name);
        if (index >= 0) {
            config->index = index;
            cached_count++;

            // 更新workflow_context中的特定索引
            if (strcmp(config->name, "sip") == 0) ctx->sip_idx = index;
            else if (strcmp(config->name, "dip") == 0) ctx->dip_idx = index;
            else if (strcmp(config->name, "sip_geo") == 0) ctx->sip_geo_idx = index;
            else if (strcmp(config->name, "dip_geo") == 0) ctx->dip_geo_idx = index;
            else if (strcmp(config->name, "url") == 0) ctx->url_idx = index;
            else if (strcmp(config->name, "domain") == 0) ctx->domain_idx = index;
            else if (strcmp(config->name, "body") == 0) ctx->body_idx = index;
            else if (strcmp(config->name, "cookies") == 0) ctx->cookies_idx = index;
            else if (strcmp(config->name, "header") == 0) ctx->header_idx = index;
            else if (strcmp(config->name, "bs_result") == 0) ctx->bs_idx = index;
            else if (strcmp(config->name, "hs_url_result") == 0) ctx->hs_url_result_idx = index;
            else if (strcmp(config->name, "hs_body_result") == 0) ctx->hs_body_result_idx = index;
            else if (strcmp(config->name, "hs_cookies_result") == 0) ctx->hs_cookies_result_idx = index;
            else if (strcmp(config->name, "hs_header_result") == 0) ctx->hs_header_result_idx = index;

            LOG_DEBUG(VAR_MGR_LOG_TAG, "Cached index for variable '%s': %d", config->name, index);
        } else {
            failed_count++;
            LOG_WARN(VAR_MGR_LOG_TAG, "Failed to get index for variable: %s", config->name);
        }
    }

    LOG_INFO(VAR_MGR_LOG_TAG, "Variable indices cached: %d success, %d failed", cached_count, failed_count);
}
