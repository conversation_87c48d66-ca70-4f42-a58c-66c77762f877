#include <errno.h>
#include <ctype.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <stddef.h>
#include <stdint.h>
#include <sds.h>
#include <cvector.h>
#include "util_tool.h"

/**
 * @brief 快速检查字符是否为分隔符
 * @param c 要检查的字符
 * @return bool 是分隔符返回true
 */
static inline bool is_separator(char c)
{
    // 使用查找表优化，避免多次比较
    static const bool separator_table[256] = {
        [' '] = true,['\t'] = true,['\n'] = true,['\r'] = true,
        ['('] = true,[')'] = true,['|'] = true,['&'] = true,
        ['+'] = true,['-'] = true,['*'] = true,['/'] = true,
        ['='] = true,['!'] = true,['<'] = true,['>'] = true,
        [','] = true,[';'] = true
    };
    return separator_table[(unsigned char)c];
}

/**
 * @brief 快速检查字符是否为标识符首字符
 * @param c 要检查的字符
 * @return bool 是标识符首字符返回true
 */
static inline bool is_identifier_start(char c)
{
    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c == '_';
}

/**
 * @brief 快速检查字符是否为标识符字符
 * @param c 要检查的字符
 * @return bool 是标识符字符返回true
 */
static inline bool is_identifier_char(char c)
{
    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '_';
}

/**
 * @brief 快速检查字符是否为数字首字符
 * @param c 要检查的字符
 * @return bool 是数字首字符返回true
 */
static inline bool is_number_start(char c)
{
    return (c >= '0' && c <= '9');
}

/**
 * @brief 清理已分配的字符串数组
 * @param keys 字符串数组
 */
static inline void cleanup_string_array(cvector_vector_type(char *) *keys)
{
    if (!keys || !*keys) return;

    for (size_t i = 0; i < cvector_size(*keys); i++) {
        free((*keys)[i]);
    }
    cvector_clear(*keys);
}

/**
 * @brief 使用strtol从字符串中提取数字到数组
 *
 * 使用标准库函数strtol进行数字解析：
 * - 更可靠的错误处理
 * - 支持多种数字格式
 * - 代码更简洁易维护
 *
 * @param str 输入字符串
 * @param numbers 输出数组
 * @param max_count 数组最大容量
 * @return int 成功提取的数字个数，失败返回-1
 */
int get_id_from_str(const char *str, long *numbers, int max_count)
{
    if (!str || !numbers || max_count <= 0) {
        return -1;
    }

    int count = 0;
    char *endptr;
    const char *p = str;

    while (*p && count < max_count) {
        // 跳过非数字字符
        while (*p && !isdigit(*p) && *p != '-' && *p != '+') {
            p++;
        }

        if (!*p) break;

        errno = 0;
        long num = strtol(p, &endptr, 10);

        // 检查转换是否成功
        if (p == endptr) {
            p++;
            continue;
        }

        // 检查溢出
        if (errno == ERANGE) {
            p = endptr;
            continue;
        }

        numbers[count++] = num;
        p = endptr;
    }

    return count;
}

/**
 * @brief 从字符串中提取标识符到动态数组（优化版本）
 *
 * 性能优化：
 * - 使用查找表替代多重比较
 * - 预估容量减少重分配
 * - 内联关键函数减少调用开销
 * - 优化内存分配策略
 *
 * @param str 输入字符串
 * @param keys 输出的字符串动态数组指针
 * @return int 成功提取的标识符个数，失败返回-1
 */
int get_key_from_str(const char *str, cvector_vector_type(char *) *keys)
{
    if (!str || !keys) {
        return -1;
    }

    // 预估标识符数量，减少重分配
    size_t str_len = strlen(str);
    size_t estimated_count = str_len / 8; // 经验值：平均8个字符一个标识符
    if (estimated_count < 4) estimated_count = 4;

    cvector_reserve(*keys, estimated_count);

    const char *p = str;
    int count = 0;

    while (*p) {
        // 快速跳过分隔符
        while (*p && is_separator(*p)) {
            p++;
        }

        if (!*p) break;

        const char *start = p;

        if (is_identifier_start(*p)) {
            // 扫描完整标识符
            while (*p && is_identifier_char(*p)) {
                p++;
            }
        } else if (is_number_start(*p)) {
            // 扫描完整数字
            while (*p && (isdigit(*p))) {
                p++;
            }
        } else {
            // 跳过无效序列
            while (*p && !is_separator(*p)) {
                p++;
            }
            continue;
        }

        // 创建标识符字符串
        size_t len = p - start;
        if (len > 0) {
            char *identifier = malloc(len + 1);
            if (!identifier) {
                cleanup_string_array(keys);
                return -1;
            }

            memcpy(identifier, start, len);
            identifier[len] = '\0';

            cvector_push_back(*keys, identifier);
            count++;
        }
    }

    return count;
}

/**
 * @brief 从字符串中提取标识符（零拷贝版本，使用SDS）
 *
 * 这个版本使用SDS字符串，避免手动内存管理
 *
 * @param str 输入字符串
 * @param keys 输出的SDS字符串动态数组指针
 * @return int 成功提取的标识符个数，失败返回-1
 */
int get_key_from_str_sds(const char *str, cvector_vector_type(sds) *keys)
{
    if (!str || !keys) {
        return -1;
    }

    const char *p = str;
    int count = 0;

    while (*p) {
        // 跳过分隔符
        while (*p && is_separator(*p)) {
            p++;
        }

        if (!*p) break;

        const char *start = p;

        // 检查标识符首字符
        if (!is_identifier_start(*p)) {
            // 跳过无效序列
            while (*p && !is_separator(*p)) {
                p++;
            }
            continue;
        }

        // 扫描完整标识符
        while (*p && is_identifier_char(*p)) {
            p++;
        }

        // 创建SDS字符串
        size_t len = p - start;
        if (len > 0) {
            sds identifier = sdsnewlen(start, len);
            if (!identifier) {
                // 清理已创建的SDS字符串
                for (size_t i = 0; i < cvector_size(*keys); i++) {
                    sdsfree((*keys)[i]);
                }
                cvector_clear(*keys);
                return -1;
            }

            cvector_push_back(*keys, identifier);
            count++;
        }
    }

    return count;
}
