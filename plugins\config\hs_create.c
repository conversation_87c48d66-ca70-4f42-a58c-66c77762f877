#include "hs_create.h"

#define HS_CREATE_LOG_TAG "HS_CREATE"

/**
 * @brief 清理单个规则项关联的PCRE资源
 * @param item 规则项
 */
static void cleanup_item_pcre(hs_item_t *item)
{
    if (!item || !item->re) {
        return;
    }

    // 释放所有PCRE句柄 - 修正：每个线程都有独立的PCRE句柄
    for (size_t i = 0; i < cvector_size(item->re); i++) {
        if (item->re[i]) {
            pcre_free(item->re[i]);
            item->re[i] = NULL;
        }
    }

    // 释放cvector容器
    cvector_free(item->re);
    item->re = NULL;
}

/**
 * @brief 安全的PCRE编译和分配
 * @param item 规则项
 * @param threads_num 线程数
 * @return bool 成功返回true，失败返回false
 */
static bool compile_pcre(hs_item_t *item, int threads_num)
{
    if (!item || !item->value || item->attrb != 1) {
        return true; // 不需要PCRE编译
    }

    // 清理已存在的PCRE资源
    if (item->re) {
        cleanup_item_pcre(item);
    }

    // 编译PCRE表达式
    int erroffset;
    const char *errptr;
    pcre *master_re = pcre_compile(item->value, PCRE_CASELESS | PCRE_DOTALL | PCRE_UTF8 | PCRE_UCP, &errptr, &erroffset, NULL);
    if (!master_re) {
        LOG_ERROR(HS_CREATE_LOG_TAG, "PCRE compile failed for '%s': %s at offset %d", item->value, errptr, erroffset);
        return false;
    }

    // 初始化PCRE向量
    cvector_init(item->re, threads_num, NULL);

    // 为每个线程创建独立的PCRE句柄
    for (int i = 0; i < threads_num; i++) {
        pcre *thread_re;
        if (i == 0) {
            // 第一个线程使用原始编译结果
            thread_re = master_re;
        } else {
            // 其他线程复制PCRE句柄
            size_t size;
            int result = pcre_fullinfo(master_re, NULL, PCRE_INFO_SIZE, &size);
            if (result != 0) {
                LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to get PCRE size info");
                // 清理已分配的资源
                for (int j = 0; j < i; j++) {
                    if (item->re[j]) {
                        pcre_free(item->re[j]);
                    }
                }
                cvector_free(item->re);
                item->re = NULL;
                return false;
            }

            thread_re = (pcre *)malloc(size);
            if (!thread_re) {
                LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to allocate memory for PCRE copy");
                // 清理已分配的资源
                for (int j = 0; j < i; j++) {
                    if (item->re[j]) {
                        pcre_free(item->re[j]);
                    }
                }
                cvector_free(item->re);
                item->re = NULL;
                return false;
            }
            memcpy(thread_re, master_re, size);
        }

        cvector_push_back(item->re, thread_re);
    }

    return true;
}

/**
 * @brief 销毁单个hs_item_t及其资源
 * @param item 要销毁的item
 */
static void hs_item_destroy(hs_item_t *item)
{
    if (!item) {
        return;
    }

    // 清理PCRE资源
    cleanup_item_pcre(item);

    // 释放sds字符串（如果使用sds）
    if (item->key) {
        sdsfree(item->key);
        item->key = NULL;
    }
    if (item->name) {
        sdsfree(item->name);
        item->name = NULL;
    }
    if (item->value) {
        sdsfree(item->value);
        item->value = NULL;
    }

    // 释放item本身
    free(item);
}

/**
 * @brief 创建并初始化hs_expr_ext_t结构
 * @param item 规则项
 * @return hs_expr_ext_t* 成功返回指针，失败返回NULL
 */
static hs_expr_ext_t *create_expr_ext(const hs_item_t *item)
{
    hs_expr_ext_t *ext_item = (hs_expr_ext_t *)calloc(1, sizeof(hs_expr_ext_t));
    if (!ext_item) {
        LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to allocate memory for hs_expr_ext_t");
        return NULL;
    }

    // 设置扩展标志
    if (item->min_length > 0) {
        ext_item->flags |= HS_EXT_FLAG_MIN_LENGTH;
        ext_item->min_length = item->min_length;
    }
    if (item->max_offset > 0) {
        ext_item->flags |= HS_EXT_FLAG_MAX_OFFSET;
        ext_item->max_offset = item->max_offset;
    }

    return ext_item;
}

/**
 * @brief 验证item是否有效
 * @param item 要验证的item
 * @return bool 有效返回true
 */
static bool is_valid_item(const hs_item_t *item)
{
    return item && item->value && strlen(item->value) > 0;
}

/**
 * @brief 清理编译数据中的资源
 */
static void cleanup_compile_data(cvector_vector_type(char *) *exprs,
                                cvector_vector_type(unsigned int) *flags,
                                cvector_vector_type(unsigned int) *ids,
                                cvector_vector_type(hs_expr_ext_t *) *ext)
{
    if (exprs) {
        cvector_free(*exprs);
        *exprs = NULL;
    }
    if (flags) {
        cvector_free(*flags);
        *flags = NULL;
    }
    if (ids) {
        cvector_free(*ids);
        *ids = NULL;
    }
    if (ext) {
        for (size_t i = 0; i < cvector_size(*ext); i++) {
            free((*ext)[i]);
        }
        cvector_free(*ext);
        *ext = NULL;
    }
}

/**
 * @brief 准备用于Hyperscan编译的数据，并预编译PCRE表达式
 * @param tdata 扩展的tdata对象
 * @param threads_num 线程数
 * @param exprs 输出：规则表达式字符串列表
 * @param flags 输出：Hyperscan编译标志列表
 * @param ids 输出：编译时分配的规则ID列表
 * @param ext 输出：Hyperscan期望的hs_expr_ext_t参数列表
 * @param valid_items 输出：有效的hs_item_t指针列表
 * @return bool 成功返回true, 失败返回false
 */
static bool build_compile_data(hs_tdata_t *tdata, int threads_num,
                                cvector_vector_type(char *) *exprs,
                                cvector_vector_type(unsigned int) *flags,
                                cvector_vector_type(unsigned int) *ids,
                                cvector_vector_type(hs_expr_ext_t *) *ext,
                                cvector_vector_type(hs_item_t *) *valid_items)
{
    // 初始化输出向量
    cvector_init(*exprs, 0, NULL);
    cvector_init(*flags, 0, NULL);
    cvector_init(*ids, 0, NULL);
    cvector_init(*ext, 0, NULL);
    cvector_init(*valid_items, 0, NULL);

    if (!tdata->items || cvector_size(tdata->items) == 0) {
        LOG_WARN(HS_CREATE_LOG_TAG, "No items to process");
        return true;
    }

    unsigned int current_id = 0;
    size_t items_count = cvector_size(tdata->items);
    size_t processed_count = 0;
    size_t failed_count = 0;

    for (size_t i = 0; i < items_count; i++) {
        hs_item_t *item = (hs_item_t *)tdata->items[i];

        if (!is_valid_item(item)) {
            LOG_WARN(HS_CREATE_LOG_TAG, "Skipping invalid item at index %zu", i);
            failed_count++;
            continue;
        }

        // 编译PCRE（如果需要）
        if (!compile_pcre(item, threads_num)) {
            LOG_WARN(HS_CREATE_LOG_TAG, "Failed to compile PCRE for item '%s', skipping", item->value);
            failed_count++;
            continue;
        }

        // 创建扩展参数结构
        hs_expr_ext_t *ext_item = create_expr_ext(item);
        if (!ext_item) {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to create ext structure for item '%s'", item->value);
            goto error;
        }

        // 添加到编译数据
        cvector_push_back(*exprs, item->value);
        //TODO: cvector_push_back(*flags, HS_FLAG_SOM_LEFTMOST | HS_FLAG_CASELESS | HS_FLAG_UTF8 | HS_FLAG_UCP);
        cvector_push_back(*flags, HS_FLAG_SOM_LEFTMOST | HS_FLAG_CASELESS);
        cvector_push_back(*ids, current_id);
        cvector_push_back(*ext, ext_item);
        cvector_push_back(*valid_items, item);

        current_id++;
        processed_count++;
    }

    LOG_INFO(HS_CREATE_LOG_TAG, "Build compile data completed: %zu processed, %zu failed, %zu total", processed_count, failed_count, items_count);

    return true;

error:
    cleanup_compile_data(exprs, flags, ids, ext);
    if (*valid_items) {
        cvector_free(*valid_items);
        *valid_items = NULL;
    }
    return false;
}

/**
 * @brief 移除失败的表达式
 * @param exprs 表达式数组
 * @param flags 标志数组
 * @param ids ID数组
 * @param ext 扩展参数数组
 * @param valid_items 有效项数组
 * @param failed_idx 失败的索引
 * @return bool 成功返回true
 */
static bool remove_failed_expression(cvector_vector_type(char *) *exprs,
                                    cvector_vector_type(unsigned int) *flags,
                                    cvector_vector_type(unsigned int) *ids,
                                    cvector_vector_type(hs_expr_ext_t *) *ext,
                                    cvector_vector_type(hs_item_t *) *valid_items,
                                    size_t failed_idx)
{
    if (failed_idx >= cvector_size(*exprs)) {
        LOG_ERROR(HS_CREATE_LOG_TAG, "Failed expression index out of range: %zu", failed_idx);
        return false;
    }

    LOG_WARN(HS_CREATE_LOG_TAG, "Removing failed expression at index %zu: '%s'", failed_idx, (*exprs)[failed_idx]);

    // 清理失败项的PCRE资源
    if (failed_idx < cvector_size(*valid_items) && (*valid_items)[failed_idx]) {
        cleanup_item_pcre((*valid_items)[failed_idx]);
    }

    // 释放ext结构内存
    if (failed_idx < cvector_size(*ext) && (*ext)[failed_idx]) {
        free((*ext)[failed_idx]);
        (*ext)[failed_idx] = NULL;
    }

    // 移除失败的表达式及其相关数据
    cvector_erase(*exprs, failed_idx);
    cvector_erase(*flags, failed_idx);
    cvector_erase(*ids, failed_idx);
    cvector_erase(*ext, failed_idx);
    cvector_erase(*valid_items, failed_idx);

    return true;
}

/**
 * @brief 使用重试逻辑编译Hyperscan数据库
 * @param db 输出：Hyperscan数据库结构
 * @param exprs 规则表达式列表
 * @param flags 编译标志列表
 * @param ids 规则ID列表
 * @param ext Hyperscan的hs_expr_ext_t参数列表
 * @param valid_items 有效的hs_item_t指针列表
 * @return bool 成功返回true, 失败返回false
 */
static bool hs_database_compile(hs_db_t *db,
                                cvector_vector_type(char *) *exprs,
                                cvector_vector_type(unsigned int) *flags,
                                cvector_vector_type(unsigned int) *ids,
                                cvector_vector_type(hs_expr_ext_t *) *ext,
                                cvector_vector_type(hs_item_t *) *valid_items)
{
    if (cvector_size(*exprs) == 0) {
        LOG_WARN(HS_CREATE_LOG_TAG, "No expressions to compile");
        db->database = NULL;
        return true;
    }

    const int MAX_RETRIES = 10;
    int retry_count = 0;

    while (retry_count < MAX_RETRIES && cvector_size(*exprs) > 0) {
        hs_compile_error_t *compile_err = NULL;

        hs_error_t err = hs_compile_ext_multi(
            (const char *const *)*exprs,
            *flags,
            *ids,
            (const hs_expr_ext_t *const *)*ext,
            cvector_size(*exprs),
            HS_MODE_BLOCK,
            NULL,
            &db->database,
            &compile_err);

        if (err == HS_SUCCESS) {
            LOG_INFO(HS_CREATE_LOG_TAG, "Hyperscan database compiled successfully with %zu expressions", cvector_size(*exprs));
            return true;
        }

        // 编译失败，尝试移除失败的表达式
        if (compile_err && compile_err->expression >= 0) {
            size_t failed_idx = (size_t)compile_err->expression;
            LOG_ERROR(HS_CREATE_LOG_TAG, "Hyperscan compile error: %s, item: %s", compile_err->message, exprs[failed_idx]);

            if (!remove_failed_expression(exprs, flags, ids, ext, valid_items, failed_idx)) {
                hs_free_compile_error(compile_err);
                break;
            }

            LOG_WARN(HS_CREATE_LOG_TAG, "Retry %d: Removed failed expression, %zu expressions remaining", retry_count + 1, cvector_size(*exprs));
        } else {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Hyperscan compile failed with unknown error (code: %d)", err);
            if (compile_err) {
                hs_free_compile_error(compile_err);
            }
            break;
        }

        if (compile_err) {
            hs_free_compile_error(compile_err);
        }
        retry_count++;
    }

    if (cvector_size(*exprs) == 0) {
        LOG_ERROR(HS_CREATE_LOG_TAG, "All expressions failed to compile");
    } else {
        LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to compile Hyperscan database after %d retries", retry_count);
    }

    return false;
}

/**
 * @brief 为每个线程分配Hyperscan的scratch空间
 * @param db Hyperscan数据库结构
 * @param threads_num 线程数
 * @return bool 如果所有scratch都分配成功则返回true
 */
static bool hs_scratch_create(hs_db_t *db, int threads_num)
{
    if (!db || !db->database || threads_num <= 0) {
        LOG_ERROR(HS_CREATE_LOG_TAG, "Invalid parameters for scratch allocation");
        return false;
    }

    // 初始化scratch向量
    cvector_init(db->scratchs, threads_num, NULL);

    for (int i = 0; i < threads_num; i++) {
        hs_scratch_t *scratch = NULL;
        hs_error_t err = hs_alloc_scratch(db->database, &scratch);

        if (err != HS_SUCCESS || !scratch) {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to allocate Hyperscan scratch space for thread %d (error: %d)",
                i, err);

            // 清理已分配的scratch空间
            for (int j = 0; j < i; j++) {
                if (db->scratchs[j]) {
                    hs_free_scratch(db->scratchs[j]);
                }
            }
            cvector_free(db->scratchs);
            db->scratchs = NULL;
            return false;
        }

        cvector_push_back(db->scratchs, scratch);
    }

    LOG_INFO(HS_CREATE_LOG_TAG, "Successfully allocated %d Hyperscan scratch spaces", threads_num);
    return true;
}

/**
 * @brief 清理数据库资源
 * @param db 数据库结构
 */
static void cleanup_database(hs_db_t *db)
{
    if (!db) {
        return;
    }

    if (db->database) {
        hs_free_database(db->database);
        db->database = NULL;
    }

    if (db->scratchs) {
        for (size_t i = 0; i < cvector_size(db->scratchs); i++) {
            if (db->scratchs[i]) {
                hs_free_scratch(db->scratchs[i]);
                db->scratchs[i] = NULL;
            }
        }
        cvector_free(db->scratchs);
        db->scratchs = NULL;
    }

    free(db);
}

/**
 * @brief 更新tdata中的items，移除无效项
 * @param tdata 目标tdata
 * @param valid_items 有效项列表
 * @return bool 成功返回true
 */
static bool update_tdata_items(hs_tdata_t *tdata, cvector_vector_type(hs_item_t *) *valid_items)
{
    items_t old_items = tdata->items;
    tdata->items = NULL;

    // 如果有有效项，创建新的items向量
    if (cvector_size(*valid_items) > 0) {
        cvector_init(tdata->items, cvector_size(*valid_items), NULL);
        for (size_t i = 0; i < cvector_size(*valid_items); i++) {
            cvector_push_back(tdata->items, (void *)(*valid_items)[i]);
        }
    }

    // 清理无效项的资源
    if (old_items) {
        for (size_t i = 0; i < cvector_size(old_items); i++) {
            hs_item_t *item = (hs_item_t *)old_items[i];
            bool is_valid = false;

            // 检查是否为有效项
            for (size_t j = 0; j < cvector_size(*valid_items); j++) {
                if (item == (*valid_items)[j]) {
                    is_valid = true;
                    break;
                }
            }

            // 销毁无效项
            if (!is_valid) {
                hs_item_destroy(item);
            }
        }
        cvector_free(old_items);
    }

    return true;
}

/**
 * @brief 创建Hyperscan数据库
 * @param arg 指向 hs_tdata_t 的指针
 * @param threads_num 线程数
 * @return bool 成功返回true，失败返回false
 */
bool hs_database_create(void *arg, int threads_num)
{
    CHECK_ERR_EXEC(!arg || threads_num <= 0, HS_CREATE_LOG_TAG, return false, "Invalid parameters: arg=%p, threads_num=%d", arg, threads_num);

    hs_tdata_t *tdata = (hs_tdata_t *)arg;
    if (!tdata->items) {
        LOG_WARN(HS_CREATE_LOG_TAG, "No items to process, creating empty database");
        tdata->ctx = NULL;
        return true;
    }

    // 分配数据库结构
    hs_db_t *db = (hs_db_t *)calloc(1, sizeof(hs_db_t));
    if (!db) {
        LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to allocate memory for hs_db_t");
        return false;
    }

    // 编译数据向量
    cvector_vector_type(char *) exprs = NULL;
    cvector_vector_type(unsigned int) flags = NULL;
    cvector_vector_type(unsigned int) ids = NULL;
    cvector_vector_type(hs_expr_ext_t *) ext = NULL;
    cvector_vector_type(hs_item_t *) valid_items = NULL;

    bool success = false;

    do {
        // 1. 准备编译数据
        if (!build_compile_data(tdata, threads_num, &exprs, &flags, &ids, &ext, &valid_items)) {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to build compile data");
            break;
        }

        // 2. 检查是否有有效项
        if (cvector_size(valid_items) == 0) {
            LOG_WARN(HS_CREATE_LOG_TAG, "No valid items to compile, creating empty database");
            success = true;
            break;
        }

        // 3. 编译数据库
        if (!hs_database_compile(db, &exprs, &flags, &ids, &ext, &valid_items)) {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to compile Hyperscan database");
            break;
        }

        // 4. 分配scratch空间
        if (db->database && !hs_scratch_create(db, threads_num)) {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to allocate scratch spaces");
            break;
        }

        // 5. 更新tdata
        if (!update_tdata_items(tdata, &valid_items)) {
            LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to update tdata items");
            break;
        }

        success = true;

    } while (0);

    // 清理编译数据
    cleanup_compile_data(&exprs, &flags, &ids, &ext);
    if (valid_items) {
        cvector_free(valid_items);
    }

    if (success) {
        tdata->ctx = db;
        LOG_INFO(HS_CREATE_LOG_TAG, "Hyperscan database created successfully");
    } else {
        cleanup_database(db);
        tdata->ctx = NULL;
        LOG_ERROR(HS_CREATE_LOG_TAG, "Failed to create Hyperscan database");
    }

    return success;
}

/**
 * @brief 销毁Hyperscan数据库及相关资源
 * @param arg 指向 hs_tdata_t 的指针
 */
void hs_database_destroy(void *arg)
{
    if (!arg) {
        LOG_WARN(HS_CREATE_LOG_TAG, "Attempt to destroy NULL tdata pointer");
        return;
    }

    hs_tdata_t *tdata = (hs_tdata_t *)arg;

    // 清理Hyperscan数据库资源
    hs_db_t *db = (hs_db_t *)tdata->ctx;
    if (db) {
        // 释放数据库
        if (db->database) {
            hs_free_database(db->database);
            db->database = NULL;
        }

        // 释放所有scratch空间
        if (db->scratchs) {
            for (size_t i = 0; i < cvector_size(db->scratchs); i++) {
                if (db->scratchs[i]) {
                    hs_free_scratch(db->scratchs[i]);
                    db->scratchs[i] = NULL;
                }
            }
            cvector_free(db->scratchs);
            db->scratchs = NULL;
        }

        free(db);
        tdata->ctx = NULL;
    }

    // 清理所有item资源
    if (tdata->items) {
        for (size_t i = 0; i < cvector_size(tdata->items); i++) {
            hs_item_t *item = (hs_item_t *)tdata->items[i];
            hs_item_destroy(item);
        }
        cvector_free(tdata->items);
        tdata->items = NULL;
    }

    LOG_INFO(HS_CREATE_LOG_TAG, "Hyperscan database and resources destroyed successfully");
}
