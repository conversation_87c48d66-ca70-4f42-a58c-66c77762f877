#include "loop_register.h"
#include "vars_manager.h"
#include "cfg_util.h"
#include "db_proc_oper.h"
#include "db_proc_loop.h"
#include "util_log.h"
#include "oper_map.h"
#include "vars_bind.h"

#define MAX_NAME_LEN 128
#define MAX_BIND_COUNT 16
#define LOOP_LOG_TAG "loop_register"

/**
 * @brief 动态注册基础变量和算子
 */
int dynamic_register_vars(workflow_ctx_t *ctx)
{
    CHECK_ERR_EXEC(!get_vars_manager() && vars_manager_init() != 0, LOOP_LOG_TAG, return -1, "Failed to initialize config manager");

    vars_manager_t *mgr = get_vars_manager();
    CHECK_ERR_EXEC(!mgr, LOOP_LOG_TAG, return -1, "Config manager not available");

    int failed_count = 0;
    int success_count = 0;

    // 注册所有变量配置
    khiter_t k;
    for (k = kh_begin(mgr->var_configs); k != kh_end(mgr->var_configs); ++k) {
        if (!kh_exist(mgr->var_configs, k)) { // 变量配置不存在，则跳过
            // failed_count++;
            // LOG_WARN(LOOP_LOG_TAG, "Variable config not found: %s", kh_key(mgr->var_configs, k));
            continue;
        }

        // 获取变量配置
        var_config_t *config = (var_config_t *)kh_value(mgr->var_configs, k);
        if (!config) {
            failed_count++;
            LOG_WARN(LOOP_LOG_TAG, "Variable config not found: %s", kh_key(mgr->var_configs, k));
            continue;
        }

        if (safe_register_variable(ctx->engine, config->name, config->type) == 0) {
            success_count++;
        } else {
            failed_count++;
        }
    }

    // 注册预定义算子实例
    for (k = kh_begin(mgr->operator_configs); k != kh_end(mgr->operator_configs); ++k) {
        if (!kh_exist(mgr->operator_configs, k)) {
            // failed_count++;
            // LOG_WARN(LOOP_LOG_TAG, "Operator config not found: %s", kh_key(mgr->operator_configs, k));
            continue;
        }

        operator_config_t *config = (operator_config_t *)kh_value(mgr->operator_configs, k);
        if (!config) {
            failed_count++;
            LOG_WARN(LOOP_LOG_TAG, "Operator config not found: %s", kh_key(mgr->operator_configs, k));
            continue;
        }

        if (safe_register_operator(ctx->engine, config->instance_name, config->operator_name, config->bindings, config->binding_count) == 0) {
            success_count++;
        } else {
            failed_count++;
        }
    }

    LOG_WARN(LOOP_LOG_TAG, "Variable registration completed: %d success, %d failed", success_count, failed_count);
    return (success_count > 0) ? 0 : -1;
}

/**
 * @brief 动态注册算子
 */
int dynamic_register_opers(workflow_ctx_t *ctx)
{
    // 初始化算子映射表
    CHECK_ERR_EXEC(init_operator_map() != 0, LOOP_LOG_TAG, return -1, "Failed to initialize operator mapping table");

    // 获取算子配置
    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(get_oper_plugin_id());
    CHECK_ERR_EXEC(!cfg_wrap || !cfg_wrap->config, LOOP_LOG_TAG, return 0, "There is no operator config, please check database");

    oper_tdata_t *tdata = (oper_tdata_t *)cfg_wrap->config;
    size_t total_items = cvector_size(tdata->items);
    size_t success_count = 0;
    size_t failed_count = 0;

    LOG_DEBUG(LOOP_LOG_TAG, "Found %zu operator items to register", total_items);

    // 动态注册算子
    for (size_t i = 0; i < total_items; i++) {
        oper_item_t *item = (oper_item_t *)tdata->items[i];
        if (!item || !item->key || !item->proto || !item->direction || !item->fields || !item->expr) {
            failed_count++;
            continue;
        }

        // 生成算子输出变量名
        char result_var[MAX_NAME_LEN];
        int ret = snprintf(result_var, sizeof(result_var), "%s_%s_%s_%s_result", item->proto, item->direction, item->fields, item->key);
        if (ret < 0 || ret >= sizeof(result_var)) {
            failed_count++;
            continue;
        }

        // 给算子实例添加变量配置
        size_t bind_count = 0;
        an_operator_binding_t bindings[MAX_BIND_COUNT] = {0};

        // 确定插件名称
        const char *plugin_name = NULL;
        switch (item->match) {
            case 0:
                plugin_name = "oper_hs_check";
                // 注册算子实例的输入输出变量
                safe_register_variable(ctx->engine, item->key, AN_VAR_TYPE_STRING);
                safe_register_variable(ctx->engine, result_var, AN_VAR_TYPE_DOUBLE);

                // 绑定算子实例的输入输出变量
                hs_check_binding_variable(item, result_var, bindings, &bind_count);

                // 添加到动态变量配置
                add_variable_config(item->key, AN_VAR_TYPE_STRING, VAR_CATEGORY_DYNAMIC);
                add_variable_config(result_var, AN_VAR_TYPE_DOUBLE, VAR_CATEGORY_OPERATOR);
                break;
            case 1:
                plugin_name = "oper_ip_geo";
                break;
            case 2:
                plugin_name = "oper_dict";
                break;
            default:
                LOG_ERROR(LOOP_LOG_TAG, "Unknown match type %d for operator: %s", item->match, item->key);
                failed_count++;
                continue;
        }

        // 生成实例名称
        char instance_name[MAX_NAME_LEN];
        snprintf(instance_name, sizeof(instance_name), "%s_%s_%s", plugin_name, item->key, item->fields);

        if (safe_register_operator(ctx->engine, instance_name, plugin_name, bindings, bind_count) == 0) {
            // 创建并添加算子映射
            oper_map_t *map = (oper_map_t *)calloc(1, sizeof(oper_map_t));
            if (map) {
                map->operator_id = sdsnew(item->key);
                map->operator_name = sdsnew(plugin_name);
                map->operator_type = sdsnew(item->fields);
                map->input_vars = sdsnew(instance_name);
                map->output_vars = sdsnew(result_var);

                if (add_operator_map(map) == 0) {
                    success_count++;
                } else {
                    LOG_ERROR(LOOP_LOG_TAG, "Failed to add operator mapping for: %s", item->key);
                    failed_count++;
                }
            }
        } else {
            failed_count++;
        }

        // 释放bindings中的内存
        binding_variable_free(plugin_name, bindings, bind_count);
    }

    an_cfg_wrapper_put(get_oper_plugin_id(), cfg_wrap);

    LOG_INFO(LOOP_LOG_TAG, "Dynamic operators registration completed: %zu success, %zu failed, %zu total", success_count, failed_count, total_items);

    print_operator_map();
    return 0;
}

/**
 * @brief 动态注册所有规则
 */
int dynamic_register_loops(workflow_ctx_t *ctx)
{
    CHECK_ERR_EXEC(!ctx || !ctx->engine, LOOP_LOG_TAG, return -1, "Invalid workflow context");

    // 获取规则配置
    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(get_loop_plugin_id());
    if (!cfg_wrap || !cfg_wrap->config) {
        LOG_WARN(LOOP_LOG_TAG, "No rule config found, skipping rule registration");
        return 0;
    }

    loop_tdata_t *tdata = (loop_tdata_t *)cfg_wrap->config;
    size_t total_items = cvector_size(tdata->items);
    size_t success_count = 0;
    size_t failed_count = 0;
    size_t disabled_count = 0;

    LOG_INFO(LOOP_LOG_TAG, "Found %zu rule items to register", total_items);

    // 动态注册规则
    for (size_t i = 0; i < total_items; i++) {
        loop_item_t *item = (loop_item_t *)tdata->items[i];
        if (!item || !item->key || !item->expr) {
            LOG_ERROR(LOOP_LOG_TAG, "Invalid rule item at index %zu", i);
            failed_count++;
            continue;
        }

        // 检查状态是否启用
        if (item->state != 1) {
            LOG_DEBUG(LOOP_LOG_TAG, "Skipping disabled rule: %s", item->key);
            disabled_count++;
            continue;
        }

        // 转换表达式中的算子ID为输出变量名
        sds converted_expr = convert_expr_operator(item->expr);
        if (!converted_expr) {
            LOG_ERROR(LOOP_LOG_TAG, "Failed to convert expression for rule: %s", item->key);
            failed_count++;
            continue;
        }
        LOG_DEBUG(LOOP_LOG_TAG, "Rule '%s' converted expression: %s --> %s", item->key, item->expr, converted_expr);

        // 注册规则表达式
        const char *rule_name = item->name ? item->name : item->key;
        if (an_calc_reg_expr(ctx->engine, rule_name, converted_expr)) {
            LOG_DEBUG(LOOP_LOG_TAG, "Successfully registered rule: %s -> %s", rule_name, converted_expr);
            success_count++;
        } else {
            LOG_ERROR(LOOP_LOG_TAG, "Failed to register rule expression: %s -> %s", rule_name, converted_expr);
            failed_count++;
        }

        sdsfree(converted_expr);
    }

    an_cfg_wrapper_put(get_loop_plugin_id(), cfg_wrap);

    LOG_INFO(LOOP_LOG_TAG, "Dynamic rules registration completed: %zu success, %zu failed, %zu disabled, %zu total",
             success_count, failed_count, disabled_count, total_items);

    return 0;
}

/**
 * @brief 动态缓存变量索引
 */
void dynamic_cache_variables_index(workflow_ctx_t *ctx)
{
    cache_all_variable_indices(ctx);
}
