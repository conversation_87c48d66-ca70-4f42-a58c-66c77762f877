#include "cfg_util.h"
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <sys/wait.h>
#include "db_proc_snort.h"

/**
 * @brief 销毁snort数据结构
 *
 * @param arg snort数据结构指针
 * @return int 成功返回CFG_SUCCESS
 */
static int snort_tdata_destroy(const void *arg)
{
    if (!arg) {
        return CFG_SUCCESS;
    }

    snort_tdata_t *tdata = (snort_tdata_t *)arg;

    cvector_free(tdata->items);

    free(tdata);

    return CFG_SUCCESS;
}

/**
 * @brief 销毁snort项目
 *
 * @param arg snort项目指针
 */
static void snort_item_destroy(void *arg)
{
    if (!arg) return;

    snort_item_t *item = (snort_item_t *)arg;

    if (item->key) {
        sdsfree(item->key);
    }

    if (item->rule_content) {
        sdsfree(item->rule_content);
    }

    free(item);
}

/**
 * @brief 创建snort项目
 *
 * @param item_arg 项目指针
 * @param res PG查询结果
 * @param row 行索引
 * @return int 成功返回CFG_SUCCESS，失败返回CFG_FAILURE
 */
static int snort_item_create(items_t *items, PGresult *res, size_t row)
{
    // 获取字段值
    const char *key_str = PQgetvalue(res, row, 0);
    const char *rule_content = PQgetvalue(res, row, 1);
    const char *type_str = PQgetvalue(res, row, 2);

    // 验证字段值
    CHECK_ERR_EXEC(!key_str || !rule_content || !type_str, CFG_LOG_TAG, return CFG_FAILURE, "Missing field values at row %zu", row);

    snort_item_t *item = (snort_item_t *)calloc(1, sizeof(snort_item_t));
    if (!item) {
        return CFG_FAILURE;
    }

    // 设置key字段
    item->key = sdsnew(key_str);
    CHECK_ERR_EXEC(!item->key, CFG_LOG_TAG, free(item); return CFG_FAILURE, "Failed to allocate key string at row %zu", row);

    // 设置rule_content字段
    item->rule_content = sdsnew(rule_content);
    CHECK_ERR_EXEC(!item->rule_content, CFG_LOG_TAG, sdsfree(item->key); free(item); return CFG_FAILURE, "Failed to allocate rule content at row %zu", row);

    // 转换type字段
    uint64_t type = 0;
    CHECK_ERR_EXEC(!cfg_str_to_uint64(type_str, &type), CFG_LOG_TAG, sdsfree(item->key); sdsfree(item->rule_content); return CFG_FAILURE, "Failed to parse type field at row %zu", row);
    item->type = type;

    // 设置默认状态为启用
    item->state = 0;

    cvector_push_back(*items, item);

    return CFG_SUCCESS;
}

/**
 * @brief 执行reload命令
 *
 * @param reload_cmd 要执行的命令
 * @return bool 成功返回true，失败返回false
 */
static bool cfg_execute_reload_command(const char *reload_cmd)
{
    CHECK_ERR_EXEC(!reload_cmd || strlen(reload_cmd) == 0, CFG_LOG_TAG, return true, "No reload command specified, skipping.");

    int ret = system(reload_cmd);
    CHECK_ERR_EXEC(ret != 0, CFG_LOG_TAG, return false, "Reload command returned error code: %d, %s", ret, strerror(errno));

    return true;
}

/**
 * @brief 后处理函数，将规则写入文件
 *
 * @param tdata 数据结构指针
 * @return int 成功返回CFG_SUCCESS，失败返回CFG_FAILURE
 */
static int snort_tdata_post_process(void *arg)
{
    snort_tdata_t *tdata = (snort_tdata_t *)arg;
    if (!tdata || !g_conf) {
        return CFG_FAILURE;
    }

    // 打开snort规则文件
    int fd = open(g_conf->snort_file, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    CHECK_ERR_EXEC(fd == -1, CFG_LOG_TAG, return CFG_FAILURE, "Failed to open snort rule file '%s': %s", g_conf->snort_file, strerror(errno));

    // 写入所有规则
    for (size_t i = 0; i < cvector_size(tdata->items); i++) {
        snort_item_t *item = (snort_item_t *)tdata->items[i];
        CHECK_ERR_EXEC(!item || !item->rule_content, CFG_LOG_TAG, return CFG_FAILURE, "Invalid item at row %zu", i);

        // 准备写入缓冲区
        char buf[4096] = {0};
        int len = snprintf(buf, sizeof(buf), "%s\n", item->rule_content);
        CHECK_ERR_EXEC(len < 0 || (size_t)len >= sizeof(buf), CFG_LOG_TAG, return CFG_FAILURE, "Failed to write rule to file: %s", strerror(errno));

        // 写入文件
        CHECK_ERR_EXEC(!cfg_safe_write(fd, buf, len), CFG_LOG_TAG, return CFG_FAILURE, "Failed to write rule to file: %s", strerror(errno));
    }

    // 执行reload命令
    CHECK_ERR_EXEC(!cfg_execute_reload_command(g_conf->snort_reload_cmd), CFG_LOG_TAG, return CFG_FAILURE, "Failed to execute reload command");

    return CFG_SUCCESS;
}

static const cfg_module_create_t module_desc = {
    .expect_cols = API_SNORT_COL_NUM,
    .table_name = API_SNORT_TABLE,
    .redis_key = API_REDIS_KEY_SNORT,
    .tdata_size = sizeof(snort_tdata_t),
    .item_size = sizeof(snort_item_t),
    .create_item = snort_item_create,
    .destroy_item = snort_item_destroy,
    .post_process = snort_tdata_post_process,
    .destroy_tdata = snort_tdata_destroy,
};

static void *snort_tdata_create(void *arg)
{
    return cfg_module_create((void *)&module_desc);
}

static config_plugin_t snort_plugin = {
    .name = "snort",
    .check_interval = 1,
    .grace_period = 3,
    .create = snort_tdata_create,
    .destroy = snort_tdata_destroy,
};

int snort_config_init(plugin_ctx_t *ctx)
{
    snort_plugin.user = ctx;
    ctx->plugin_id = an_cfg_plugin_register(&snort_plugin);
    return ctx->plugin_id;
}
