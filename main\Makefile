include $(DSAP_BUILD_MACROS_MK)

#$(error $(shell pkg-config --cflags libxlib libip2location))
CFLAGS_LOCAL += $(shell pkg-config --cflags libxlib libip2location)
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_cfg_svc -I $(INSTALL_ROOT_INCLUDE)/an_util -I $(INSTALL_ROOT_INCLUDE)/json_wrapper
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/zmq -I $(INSTALL_ROOT_INCLUDE)/an_frame -I $(INSTALL_ROOT_INCLUDE)/an_cli
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/yyjson -I $(INSTALL_ROOT_INCLUDE)/an_calc
CFLAGS_LOCAL +=  -I $(INSTALL_ROOT_INCLUDE)/util_tool -I $(INSTALL_ROOT_INCLUDE)/cfg_svc

LDFLAGS += $(shell pkg-config --libs libxlib libip2location)
#LDFLAGS += -L$(INSTALL_ROOT_LIB)/ -lzlog -lconfig_svc -lan_util
LDFLAGS += -lzlog -lan_cfg_svc -lan_util -lan_calc -lan_expr -lyyjson -lpcre2-8 -lan_frame -lyaml -lzmq -lan_cli -lcurl -lstdc++ -ldl
LDFLAGS += -lutil_tool -lpcre -lcfg_svc
#LDFLAGS += libasan.a

#CFLAGS += -fsanitize=address -fno-optimize-sibling-calls -fno-omit-frame-pointer
CFLAGS  += -std=gnu99 -DUNITTESTS=1
BUILD_TYPE = bin
BUILD_NAME = risk
BUILD_VERSION = 1
INSTALL_APEEND_PATH =
BUILD_DYNLIB_PGKCONFIG = 1

#INCLUDE_FILES += risk_base_redis.h

include $(DSAP_BUILD_RULES_MK)
