#ifndef __DB_PROC_OPER_H__
#define __DB_PROC_OPER_H__

typedef base_tdata_t oper_tdata_t;

typedef struct {
    sds key;        // key
    sds proto;      // 协议名称
    sds direction;  // 方向
    sds fields;     // 识别字段
    sds expr;       // 表达式
    sds depend;     // 链接字段
    int match;      // 匹配类型: 1-hs;2-ip;3-hash
} oper_item_t;

/**
 * @brief 初始化协议解析配置
 *
 * @return int 成功返回插件ID，失败返回负值
 */
int oper_config_init(void);

/**
 * @brief 获取协议解析插件ID
 *
 * @return int 插件ID，失败返回-1
 */
int get_oper_plugin_id(void);

 /**
 * @brief 获取协议解析配置项
 *
 * @param oper_id 配置项ID
 * @return const oper_item_t* 配置项指针，失败返回NULL
 */
oper_item_t *get_oper_item_by_key(const char *key);

#endif // __DB_PROC_OPER_H__