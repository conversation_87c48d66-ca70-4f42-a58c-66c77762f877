#include <arpa/inet.h>
#include <sys/types.h>
#include "ip2lookup.h"
#include "sds.h"
#include "util_ip.h"

#define IP_DELIMITER ";,"

#ifdef RADIX_IP
static int ip_match(void *data, void *arg)
{
    (void)data; // 声明参数未使用
    bool *match = (bool *)arg;
    *match = true;
    //printf("match %s\n", (char *)arg);
    return 0; // 添加返回语句
}
#else
static int ip_match(void *data, void *arg)
{
    (void)data; // 声明参数未使用
    bool *match = (bool *)arg;
    *match = true;
    //printf("match %s\n", (char *)arg);
    return 0; // 添加返回语句
}
#endif

#ifdef RADIX_IP
void white_ip_add_radix(radix_tree_t *tree, char *cidr)
{
    prefix_t prefix;
    const char *errmsg = NULL;
    if (!prefix_pton_ex(&prefix, cidr, -1, &errmsg)) {
        return;
    }

    /* find ip-addres in obj */
    if (!radix_search_best(tree, &prefix)) {
        /* add to tree */
        radix_lookup(tree, &prefix);
    }
}
#endif

static void white_ip_add(void *tree, char *start, char *end)
{
#ifdef RADIX_IP
    cvector_vector_type(sds) blocks = NULL;
    if (end) {
        cvector_init(blocks, 0, NULL);
        //得到数组
        // TODO: 判断IP 类型
        ipv4_range_add(start, end, &blocks);
        for (int i = 0; i < cvector_size(blocks); i++) {
            white_ip_add_radix(tree, blocks[i]);
            sdsfree(blocks[i]);
        }
        cvector_free(blocks);
    } else {
        white_ip_add_radix(tree, start);
    }
#else
    ip2lookup_add((ip2lookup_t *)tree, start, end, NULL, NULL);
#endif
}

/**
 * @brief 解析单个IP规格字符串（如 "*******", "*******-*******0", "*******/24"）
 *        并将其添加到IP树中。
 *
 * @param ip_str 单个IP规格的sds字符串。
 * @param tree   目标IP树的指针。
 */
static void processor_single_ip(char *ip_str, void *tree)
{
    // 防御性编程：处理空的或无效的规格字符串
    if (!ip_str || strlen(ip_str) == 0) {
        return;
    }

    // 方案一：处理CIDR格式 (e.g., "***********/24")
    // CIDR格式被视为一个点，起始和结束IP相同，由 white_ip_add 内部处理
    if (strchr(ip_str, '/')) {
        white_ip_add(tree, ip_str, ip_str);
        return; // 处理完毕，直接返回
    }

    // 方案二：处理IP范围或单个IP (e.g., "***********-*************" or "***********")
    int rslt_count = 0;
    sds *rslt = sdssplitlen(ip_str, strlen(ip_str), "-", 1, &rslt_count);
    if (!rslt) {
        return; // 内存分配失败
    }

    switch (rslt_count) {
    case 1: // 单个IP地址
        white_ip_add(tree, rslt[0], rslt[0]);
        break;
    case 2: // IP范围
        white_ip_add(tree, rslt[0], rslt[1]);
        break;
    default:
        // 格式错误 (e.g., "*******-*******-*******")，忽略此条目
        // 可以在这里添加日志记录
        fprintf(stderr, "IP format error: %s\n", ip_str);
        break;
    }

    sdsfreesplitres(rslt, rslt_count);
}

/**
 * @brief 将一个包含多个IP规格的字符串解析并添加到IP树中。
 *
 * @param values 以IP_DELIMITER分隔的IP规格字符串。
 * @param tree   目标IP树的指针。
 */
void util_add_ip_tree(const char *values, void *tree)
{
    // 1. 防御性检查
    if (!values || !tree) {
        return;
    }

    // 2. 分割整个输入字符串
    int rslt_count = 0;
    sds *rslt = sdssplitlen(values, strlen(values), IP_DELIMITER, 1, &rslt_count);
    if (!rslt) {
        return; // 内存分配失败
    }

    // 3. 遍历所有IP规格，并委托给辅助函数处理
    for (size_t i = 0; i < (size_t)rslt_count; i++) {
        processor_single_ip(rslt[i], tree);
    }

    // 4. 清理资源
    sdsfreesplitres(rslt, rslt_count);
}

/**
 * @brief 释放IP树
 *
 * @param arg 树的指针
 */
void util_free_ip_tree(void *arg)
{
    if (!arg) {
        return;
    }

#ifdef RADIX_IP
    radix_tree_t *ip_tree = (radix_tree_t *)arg;
    if (ip_tree) {
        Destroy_Radix(ip_tree, NULL, NULL);
    }
#else
    ip2lookup_t *ip_tree = (ip2lookup_t *)arg;
    if (ip_tree) {
        ip2lookup_destroy(ip_tree);
    }
#endif
    arg = NULL;
}

/**
 * @brief 匹配IP
 *
 * @param tree 树
 * @param ip IP地址
 * @return int 0: 命中, -1: 未命中
 */
int util_match_ip(void *tree, char *ip)
{
#ifdef RADIX_IP
    prefix_t prefix;
    const char *errmsg = NULL;
    // 将 IP地址 转为二进制格式，便于比较
    prefix_pton_ex(&prefix, ip, -1, &errmsg);
    if (errmsg) {
        return -1;
    }

    if (radix_search_best(tree, &prefix)) {
        return 0;
    }
#else
    bool match = false;
    ip2lookup_match_str(tree, ip, true, ip_match, &match);
    if (match) { //命中
        return 0;
    }
#endif
    return -1;
}
