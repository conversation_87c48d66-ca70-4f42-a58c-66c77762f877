#include "cfg_util.h"
#include "db_proc_dict.h"

static int dict_plugin_id = -1;

static int dict_tdata_destroy(const void *arg)
{
    if (!arg) {
        return CFG_SUCCESS;
    }

    dict_tdata_t *tdata = (dict_tdata_t *)arg;

    // 释放向量（会自动调用dict_item_destroy释放每个item）
    cvector_free(tdata->items);

    // 释放哈希表（不需要释放值，因为它们是指向items中元素的指针）
    khash_t(dict_hash) *hash = (khash_t(dict_hash) *)tdata->ctx;
    if (hash) {
        kh_destroy(dict_hash, hash);
    }

    free(tdata);
    return CFG_SUCCESS;
}

static void dict_item_destroy(void *arg)
{
    if (!arg) {
        return;
    }

    dict_item_t *item = (dict_item_t *)arg;

    if (item->key) {
        sdsfree(item->key);
    }

    if (item->name) {
        sdsfree(item->name);
    }

    if (item->value) {
        sdsfree(item->value);
    }

    free(item);
}

static int dict_item_create(items_t *items, PGresult *res, size_t row)
{
    const char *key = PQgetvalue(res, row, 0);
    const char *name = PQgetvalue(res, row, 1);
    const char *values = PQgetvalue(res, row, 2);

    if (!key || !name || !values) {
        return CFG_FAILURE;
    }

    // 使用 sdssplitlen 分割 value 字符串
    int count = 0;
    sds *tokens = sdssplitlen(values, strlen(values), COMMON_SEPARATOR, 1, &count);
    if (!tokens) {
        return CFG_FAILURE; // 或者根据业务逻辑认为是CFG_SUCCESS
    }

    // 为每个分割出的子 value 创建一个 dict_item_t
    for (size_t i = 0; i < count; i++) {
        dict_item_t *item = (dict_item_t *)calloc(1, sizeof(dict_item_t));
        if (!item) {
            // 错误处理：需要释放已成功创建的 token 和 item
            sdsfreesplitres(tokens, count);
            // 这里可能还需要清理已经 push_back 到 items 里的 item，比较复杂。
            // 一个简单的策略是如果失败就整体失败。
            return CFG_FAILURE;
        }

        // 注意：key 和 name 对于这组 item 来说是共享的
        item->key = sdsnew(key);
        item->name = sdsnew(name);
        item->value = sdsnew(tokens[i]); // 使用分割后的子 value

        if (!item->key || !item->name || !item->value) {
            dict_item_destroy(item); // 使用你已有的清理函数
            sdsfreesplitres(tokens, count);
            return CFG_FAILURE;
        }
        cvector_push_back(*items, item);
    }

    sdsfreesplitres(tokens, count);
    return CFG_SUCCESS;
}

static dict_item_t *dict_item_deep_copy(const dict_item_t *item)
{
    if (!item) {
        return NULL;
    }

    dict_item_t *new_item = (dict_item_t *)calloc(1, sizeof(dict_item_t));
    if (!new_item) {
        return NULL;
    }

    new_item->key = sdsdup(item->key);
    new_item->name = sdsdup(item->name);
    new_item->value = sdsdup(item->value);

    return new_item;
}

static int dict_tdata_post_process(void *arg)
{
    dict_tdata_t *tdata = (dict_tdata_t *)arg;
    CHECK_ERR_EXEC(!tdata || !tdata->items, CFG_LOG_TAG, return CFG_FAILURE, "Invalid tdata or items");

    // 创建哈希表
    khash_t(dict_hash) *hash = kh_init(dict_hash);
    CHECK_ERR_EXEC(!hash, CFG_LOG_TAG, return CFG_FAILURE, "Failed to initialize hash table");

    // 将所有项添加到哈希表
    size_t items_count = cvector_size(tdata->items);
    for (size_t i = 0; i < items_count; i++) {
        dict_item_t *item = (dict_item_t *)tdata->items[i];
        CHECK_ERR_EXEC(!item || !item->value, CFG_LOG_TAG, return CFG_FAILURE, "Invalid item or key");

        // 检查这个 value 是否已经存在于哈希表中
        khiter_t k = kh_get(dict_hash, hash, item->value);
        if (k != kh_end(hash)) {
            // value 已存在，我们选择不覆盖，只索引第一个遇到的
            continue;
        }

        int ret;
        // value 不存在，插入
        k = kh_put(dict_hash, hash, item->value, &ret);
        if (ret > 0) { // 插入成功
            kh_val(hash, k) = item;
        } else { // 理论上不应该到这里，因为我们已经检查过了
            CHECK_ERR_EXEC(1, CFG_LOG_TAG, return CFG_FAILURE, "Failed to insert value into hash table: %s", item->value);
            kh_destroy(dict_hash, hash);
            return CFG_FAILURE;
        }
    }

    // 存储哈希表
    tdata->ctx = hash;
    return CFG_SUCCESS;
}

static const cfg_module_create_t module_desc = {
    .expect_cols = API_DICT_COL_NUM,
    .table_name = API_DICT_TABLE,
    .redis_key = API_REDIS_KEY_DICT,
    .tdata_size = sizeof(dict_tdata_t),
    .item_size = sizeof(dict_item_t),
    .create_item = dict_item_create,
    .destroy_item = dict_item_destroy,
    .post_process = dict_tdata_post_process,
    .destroy_tdata = dict_tdata_destroy,
};

static void *dict_tdata_create(void *arg)
{
    return cfg_module_create((void *)&module_desc);
}

static config_plugin_t dict_plugin = {
    .name = "dict",
    .check_interval = 1,
    .grace_period = 3,
    .create = dict_tdata_create,
    .destroy = dict_tdata_destroy,
};

int dict_config_init()
{
    dict_plugin_id = an_cfg_plugin_register(&dict_plugin);
    return dict_plugin_id;
}

const dict_item_t *dict_item_get_by_value(const char *value)
{
    if (!value || dict_plugin_id < 0) {
        return NULL;
    }

    const config_wrap_t *cfg_wrap = an_cfg_wrapper_get(dict_plugin_id);
    CHECK_ERR_EXEC(!cfg_wrap || !cfg_wrap->config, CFG_LOG_TAG, return NULL, "Invalid cfg_wrap or config");

    dict_tdata_t *tdata = (dict_tdata_t *)cfg_wrap->config;
    khash_t(dict_hash) *hash = (khash_t(dict_hash) *)tdata->ctx;
    if (!hash) {
        an_cfg_wrapper_put(dict_plugin_id, cfg_wrap);
        return NULL;
    }

    khiter_t k = kh_get(dict_hash, hash, value);
    if (k == kh_end(hash)) { // 未找到
        an_cfg_wrapper_put(dict_plugin_id, cfg_wrap);
        return NULL;
    }

    // 找到了，直接返回 item 指针
    dict_item_t *item = dict_item_deep_copy(kh_val(hash, k));

    an_cfg_wrapper_put(dict_plugin_id, cfg_wrap);

    return item;
}
