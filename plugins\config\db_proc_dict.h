#ifndef __DB_PROC_DICT_H__
#define __DB_PROC_DICT_H__

#include "cfg_util.h"
#include "khash.h"

typedef base_tdata_t dict_tdata_t;

// 定义字典项结构
typedef struct {
    sds key;        // 字典键
    sds name;       // 字典名称
    sds value;      // 字典值
} dict_item_t;

// 定义哈希表类型，使用key作为键，存储dict_item_t指针
KHASH_MAP_INIT_STR(dict_hash, dict_item_t *)

/**
 * @brief 初始化字典配置
 *
 * @return int 成功返回插件ID，失败返回负值
 */
int dict_config_init(void);

/**
 * @brief 根据value查找字典项
 *
 * @param value 要查找的值
 * @return const dict_item_t* 找到的字典项，如果未找到返回NULL
 */
const dict_item_t *dict_item_get_by_value(const char *value);

#endif // __DB_PROC_DICT_H__