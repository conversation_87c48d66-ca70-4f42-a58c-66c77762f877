#ifndef __PLUGIN_API_H__
#define __PLUGIN_API_H__

#include <an_pipeline.h>
#include "an_config.h"
#include <an_mpool.h>
#include <cvector.h>

// Forward-declare all plugin context types
typedef struct json_parser_ctx_s json_parser_ctx_t;
typedef struct workflow_ctx_s workflow_ctx_t;
typedef struct es_writer_ctx_s es_writer_ctx_t;

/**
 * @brief Creates a plugin context from the YAML config.
 * @param global_pools Vector of global memory pools.
 * @param config The application config handle. If the plugin has 'params',
 *               the parser will be positioned *inside* the params mapping
 *               when this function is called.
 * @return A pointer to the created context, or NULL on failure.
 */

// JSON Parser Plugin API
json_parser_ctx_t *plugin_json_parser_ctx_create(cvector_vector_type(perf_mempool_t *) global_pools, an_config_node_t *params_node);
void plugin_json_parser_ctx_destroy(json_parser_ctx_t* ctx);
plugin_t plugin_json_parser_get(json_parser_ctx_t* ctx);

// Workflow Plugin API
workflow_ctx_t* plugin_workflow_ctx_create(cvector_vector_type(perf_mempool_t*) global_pools, an_config_node_t* params_node);
void plugin_workflow_ctx_destroy(workflow_ctx_t* ctx);
plugin_t plugin_workflow_get(workflow_ctx_t* ctx);

// ES Writer Plugin API
es_writer_ctx_t* plugin_es_writer_ctx_create(cvector_vector_type(perf_mempool_t*) global_pools, an_config_node_t* params_node);
void plugin_es_writer_ctx_destroy(es_writer_ctx_t* ctx);
plugin_t plugin_es_writer_get(es_writer_ctx_t* ctx);

#endif // __PLUGIN_API_H__
