#include <an_calc.h>
#include <db_proc_business.h>

#define BUSINESS_LOG_TAG "oper_business"

typedef struct {
    int sip_index;
    int domain_index;
    int output_index;
    const business_item_t *item;
} oper_business_ctx_t;

#define BUSINESS_MATCH_TYPE_IP 1.0
#define BUSINESS_MATCH_TYPE_DOMAIN 2.0

static const char *const LOGICAL_NAME_BUSINESS_INPUT_SIP = "bs_input_sip";
static const char *const LOGICAL_NAME_BUSINESS_INPUT_DOMAIN = "bs_input_domain";
static const char *const LOGICAL_NAME_BUSINESS_OUTPUT_ITEM = "bs_result";

static const an_operator_variable_req_t business_dependencies[] = {
    {LOGICAL_NAME_BUSINESS_INPUT_SIP, AN_VAR_TYPE_STRING},
    {LOGICAL_NAME_BUSINESS_INPUT_DOMAIN, AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN} // Terminator
};

static const an_operator_variable_req_t business_outputs[] = {
    {LOGICAL_NAME_BUSINESS_OUTPUT_ITEM, AN_VAR_TYPE_POINTER},
    {NULL, AN_VAR_TYPE_UNKNOWN} // Terminator
};

static const an_operator_variable_req_t *business_get_deps()
{
    return business_dependencies;
}

static const an_operator_variable_req_t *business_get_outs()
{
    return business_outputs;
}

static void oper_business_reset(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }
    oper_business_ctx_t *ctx = (oper_business_ctx_t *)oper_ctx->user;

    business_item_destroy((void *)ctx->item);
    ctx->item = NULL;
}

static double oper_business_execute(an_calc_session_t *session, an_calc_oper_ctx_t *oper_ctx)
{
    CHECK_ERR_EXEC(!session || !oper_ctx || !oper_ctx->user, BUSINESS_LOG_TAG, return 0.0, "Invalid input parameters");

    LOG_DEBUG(BUSINESS_LOG_TAG, "oper_business_execute: %p", oper_ctx->user);

    oper_business_ctx_t *ctx = (oper_business_ctx_t *)oper_ctx->user;
    CHECK_ERR_EXEC(ctx->sip_index < 0 || ctx->domain_index < 0 || ctx->output_index < 0, BUSINESS_LOG_TAG, return 0.0, "bs ctx err: %p: %d, %d, %d",
                    ctx, ctx->sip_index, ctx->domain_index, ctx->output_index);

    char *sip = an_calc_get_string(session, ctx->sip_index);
    char *domain = an_calc_get_string(session, ctx->domain_index);

    if (domain && strlen(domain) > 0) {
        ctx->item = get_business_item_by_domain(domain);
    } else if (sip && strlen(sip) > 0) {
        ctx->item = get_business_item_by_ip(sip);
    } else {
        LOG_ERROR(BUSINESS_LOG_TAG, "Invalid business input: %s, %s", sip, domain);
        return 0.0;
    }

    if (!ctx->item) {
        an_calc_set_pointer(session, ctx->output_index, NULL);
        return 0.0;
    } else {
        an_calc_set_pointer(session, ctx->output_index, (void *)ctx->item);
        return 100.0;
    }
}

static int oper_business_init(an_calc_oper_ctx_t *oper_ctx)
{
    oper_business_ctx_t *ctx = (oper_business_ctx_t *)calloc(1, sizeof(oper_business_ctx_t));
    if (!ctx) {
        return -1;
    }

    ctx->item = NULL;
    ctx->sip_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_BUSINESS_INPUT_SIP);
    ctx->domain_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_BUSINESS_INPUT_DOMAIN);
    ctx->output_index = oper_ctx->get_output_index(oper_ctx, LOGICAL_NAME_BUSINESS_OUTPUT_ITEM);

    if (ctx->sip_index < 0 || ctx->domain_index < 0 || ctx->output_index < 0) {
        LOG_ERROR(BUSINESS_LOG_TAG, "Failed to resolve dependencies: sip_index: %d, domain_index: %d, output_index: %d", ctx->sip_index, ctx->domain_index, ctx->output_index);
        free(ctx);
        return -1;
    }

    oper_ctx->user = ctx;

    LOG_DEBUG(BUSINESS_LOG_TAG, "Successfully resolved dependencies oper_busines: sip_index: %d, domain_index: %d, output_index: %d -- %p",
                                ctx->sip_index, ctx->domain_index, ctx->output_index, ctx);

    return 0;
}

static void oper_business_cleanup(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    oper_business_reset(oper_ctx);

    free(oper_ctx->user);
    oper_ctx->user = NULL;
}

an_operator_plugin_t oper_business = {
    .name = "oper_business",
    .version = "1.0.0",
    .get_dependencies = business_get_deps,
    .get_outputs = business_get_outs,
    .init = oper_business_init,
    .fini = oper_business_cleanup,
    .execute = oper_business_execute,
    .reset = oper_business_reset
};
