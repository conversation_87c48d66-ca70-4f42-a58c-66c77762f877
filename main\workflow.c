#include "plugin_api.h"
#include "common.h"
#include <an_common.h>
#include <an_calc.h>
#include <string.h>
#include <stdlib.h>
#include "loop_register.h"
#include "workflow.h"
#include <common.h>

#define LOG_TAG "plugin_workflow"

static int workflow_process_init(void *context, int thread_num)
{
    workflow_ctx_t *ctx = (workflow_ctx_t *)context;
    ctx->thread_count = thread_num;
    ctx->sessions = calloc(ctx->thread_count, sizeof(an_calc_session_t *));
    if (!ctx->sessions) {
        LOG_ERROR(LOG_TAG, "Failed to allocate memory for thread sessions.");
        return PIPELINE_ERROR_MEMORY;
    }
    return PIPELINE_SUCCESS;
}

static void workflow_process_exit(void *context)
{
    workflow_ctx_t *ctx = (workflow_ctx_t *)context;
    if (ctx) {
        free(ctx->sessions);
        ctx->sessions = NULL;
    }
}

static int workflow_thread_init(void *context, int thread_idx)
{
    workflow_ctx_t *ctx = (workflow_ctx_t *)context;
    an_session_config_t session_config = {.thread_id = thread_idx, .thread_num = ctx->thread_count};
    ctx->sessions[thread_idx] = an_calc_session_create(ctx->engine, &session_config);
    if (!ctx->sessions[thread_idx]) {
        LOG_ERROR(LOG_TAG, "Failed to create rule engine session for thread %d.", thread_idx);
        return PIPELINE_ERROR_PLUGIN_FAILED;
    }
    return PIPELINE_SUCCESS;
}

static void workflow_thread_exit(void *context, int thread_idx)
{
    workflow_ctx_t *ctx = (workflow_ctx_t *)context;
    if (ctx && ctx->sessions && ctx->sessions[thread_idx]) {
        an_calc_session_destroy(ctx->sessions[thread_idx]);
        ctx->sessions[thread_idx] = NULL;
    }
}

static int workflow_thread_process(void *context, int thread_idx, data_packet_t *packet)
{
    workflow_ctx_t *ctx = (workflow_ctx_t *)context;
    if (!packet || !packet->data || packet->size != sizeof(risk_message_t)) {
        return PIPELINE_ERROR_INVALID_PARAM;
    }
#if 1
    risk_message_t *msg = (risk_message_t *)packet->data;
    an_calc_session_t *session = ctx->sessions[thread_idx];

    an_calc_session_reset(session);
    an_calc_set_string(session, ctx->sip_idx, msg->sip);
    an_calc_set_string(session, ctx->dip_idx, msg->dip);

    an_calc_session_evaluate(session);

    msg->sip_geo = (char *)an_calc_get_string(session, ctx->sip_geo_idx);
    msg->dip_geo = (char *)an_calc_get_string(session, ctx->dip_geo_idx);

    an_calc_result_iterator_t *iter = an_calc_result_iter_init(session);
    if (iter) {
        const char *rule_name;
        double result;
        while (an_calc_result_iter_next(iter, &rule_name, &result)) {
            if (result != 0.0) {
                // Important: The rule_name from an_calc is likely temporary.
                // We should copy it if it needs to live beyond the session.
                LOG_DEBUG(LOG_TAG, "HIT RULE: %s", rule_name);
                cvector_push_back(msg->hit_rules, an_string_alloc_str(&msg->arena, rule_name));
            }
        }
        // an_calc_result_iter_destroy(iter); // Assuming a destroy function exists
    }
#endif
    packet->next = packet; // Pass the message down the pipeline
    return PIPELINE_SUCCESS;
}

workflow_ctx_t *plugin_workflow_ctx_create(cvector_vector_type(perf_mempool_t *) global_pools, an_config_node_t *params_node)
{
    sleep(1);
    (void)global_pools;
    workflow_ctx_t *ctx = calloc(1, sizeof(workflow_ctx_t));
    if (!ctx) return NULL;

    const char *plugin_path = an_config_get_string(an_config_query(params_node, "plugin_path"), NULL);
    if (!plugin_path) {
        LOG_ERROR(LOG_TAG, "Missing required 'plugin_path' parameter in config.");
        goto fail;
    }

    ctx->engine = an_calc_init(plugin_path);
    if (!ctx->engine) {
        LOG_ERROR(LOG_TAG, "Failed to initialize rule engine with path: %s", plugin_path);
        goto fail;
    }

     // 1. 注册变量实例
    if (dynamic_register_vars(ctx) != 0) {
        LOG_ERROR(LOG_TAG, "Failed to register variables");
        goto fail;
    }

    // 2. 注册算子实例
    if (dynamic_register_opers(ctx) != 0) {
        LOG_ERROR(LOG_TAG, "Failed to register operators");
        goto fail;
    }

    // 3. 注册弱点实例
    if (dynamic_register_loops(ctx) != 0) {
        LOG_ERROR(LOG_TAG, "Failed to register weaknesses");
        goto fail;
    }

    // 4. 缓存变量索引
    dynamic_cache_variables_index(ctx);

    // 5、编译规则引擎
    if (an_calc_compile(ctx->engine) != 0) {
        LOG_ERROR(LOG_TAG, "Failed to compile rule engine. Check for errors like cyclic dependencies.");
        goto fail;
    }

    return ctx;
fail:
    plugin_workflow_ctx_destroy(ctx);
    return NULL;
}

void plugin_workflow_ctx_destroy(workflow_ctx_t *ctx)
{
    if (!ctx) return;
    if (ctx->engine) {
        an_calc_destroy(ctx->engine);
    }
    // No need to free plugin_path, as it's owned by the config DOM
    free(ctx);
}

plugin_t plugin_workflow_get(workflow_ctx_t *ctx)
{
    return (plugin_t)
    {
        .name = "workflow_processor",
            .process_init = workflow_process_init,
            .process_exit = workflow_process_exit,
            .thread_init = workflow_thread_init,
            .thread_exit = workflow_thread_exit,
            .thread_process = workflow_thread_process,
            .context = ctx,
    };
}
