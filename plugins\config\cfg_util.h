#ifndef __CFG_UTIL_H__
#define __CFG_UTIL_H__

#include <stdio.h>
#include <stdlib.h>
#include <limits.h>
#include <string.h>
#include <stdbool.h>
#include <ctype.h>  // for isspace()
#include <errno.h>  // for errno and ERANGE

#include "zlog.h"
#include "sds.h"
#include "cvector.h"
#include "pcre.h"
#include "hs/hs.h"
#include "hs/hs_runtime.h"

#include "util_log.h"
#include "util_yaml.h"
#include "util_redis.h"
#include "util_pgsql.h"

#include "an_common.h"
#include "an_cfg_svc.h"

#define CFG_SUCCESS  0
#define CFG_FAILURE -1

#define CFG_LOG_TAG "CFG"
#define COMMON_SEPARATOR ";"

// redis 键值
#define API_REDIS_KEY_REGU      "regu"
#define API_REDIS_KEY_DICT      "dict"
#define API_REDIS_KEY_SNORT     "snort"
#define API_REDIS_KEY_BS        "bs"
#define API_REDIS_KEY_WHITE     "white"
#define API_REDIS_KEY_BLACK     "black"
#define API_REDIS_KEY_OPER      "oper"
#define API_REDIS_KEY_LOOP      "loop"
#define API_REDIS_KEY_IDENTITY  "identity"
#define API_REDIS_KEY_MERGE     "merge"

// 定义表明
#define API_REGU_TABLE      "api_engine_entity_regex"   // 正则表
#define API_DICT_TABLE      "api_engine_entity_dict"    // 字典表
#define API_LOOP_TABLE      "api_engine_entity_loop"    // 弱点表
#define API_SNORT_TABLE     "api_engine_entity_snort"   // snort 规则表
#define API_BS_TABLE        "api_engine_entity_bs"      // 业务系统表
#define API_OPER_TABLE      "api_engine_entity_oper"    // 协议解析表
#define API_WHITE_TABLE     "api_engine_entity_white"   // 白名单过滤表

// 定义每个表的字段个数
#define API_REGU_COL_NUM    7
#define API_DICT_COL_NUM    4
#define API_OPER_COL_NUM    7
#define API_SNORT_COL_NUM   3
#define API_LOOP_COL_NUM    6
#define API_BS_COL_NUM      6
#define API_WHITE_COL_NUM   5

#define CFG_MAX_LEN 256

// 全局配置结构体
typedef struct {
    char pg_conn[CFG_MAX_LEN];      // PostgreSQL连接字符串
    char redis_host[CFG_MAX_LEN];   // Redis主机
    int  redis_port;                // Redis端口
    int  redis_db;                  // Redis数据库
    char redis_key[CFG_MAX_LEN];    // Redis键
    char http_host[CFG_MAX_LEN];    // HTTP服务主机
    int  http_port;                 // HTTP服务端口
    int  mode;                      // 模式 1: 数据库 2: restful
    int  check_cycle;               // 检查周期
    char snort_file[CFG_MAX_LEN];   // snort规则文件
    char snort_reload_cmd[CFG_MAX_LEN]; // snort重载命令
} cfg_conf_t;

// 声明全局变量
extern cfg_conf_t *g_conf;

typedef struct {
    int plugin_id;
    int thread_num;
    const char *name;
    void *user;
} plugin_ctx_t;

typedef cvector_vector_type(void *) items_t;

// 通用数据对象基类
typedef struct {
    void *ctx;     // 用户上下文
    items_t items; // 通用item容器
} base_tdata_t;

/**
 * @brief 从数据库结果创建item并添加到items容器的回调函数
 * @param items 指向items容器的指针
 * @param res PostgreSQL查询结果
 * @param row 当前处理的行号
 * @return int 成功返回CFG_SUCCESS，失败返回CFG_FAILURE
 */
typedef int (*item_create_cb)(items_t *items, PGresult *res, size_t row);

/**
 * @brief 销毁单个item的回调函数
 * @param item 要销毁的item
 */
typedef void (*item_destroy_cb)(void *item);

/**
 * @brief tdata后处理回调函数
 * @param tdata 指向tdata的指针
 * @return int 成功返回CFG_SUCCESS，失败返回CFG_FAILURE
 */
typedef int (*post_process_cb)(void *tdata);

/**
 * @brief 销毁具体tdata的回调函数
 * @param tdata 要销毁的tdata
 * @return int 成功返回CFG_SUCCESS，失败返回CFG_FAILURE
 */
typedef int (*tdata_destroy_cb)(const void *tdata);

typedef struct {
    int expect_cols;         // 期望的列数
    const char *table_name;  // 表名
    const char *redis_key;   // Redis键名

    size_t item_size;   // item的大小
    size_t tdata_size;  // tdata的大小

    item_create_cb create_item;    // 创建item的回调
    item_destroy_cb destroy_item;   // 销毁item的回调
    post_process_cb post_process;   // 后处理回调
    tdata_destroy_cb destroy_tdata; // 销毁tdata的回调
} cfg_module_create_t;

/**
 * @brief 从模块描述符创建tdata
 * @param desc 模块描述符
 * @return void* 返回创建的tdata指针，失败返回NULL
 */
void *cfg_module_create(void *arg);

/**
 * @brief 将字符串安全地转换为整型，并记录详细错误日志。
 *
 * @param str 输入的C风格字符串。
 * @return int 如果转换完全成功，返回转换后的整型值；否则返回 -1。
 */
int cfg_str_to_int(const char *str);

/**
 * @brief 将字符串安全地转换为无符号长整型 (uint64_t)，并记录详细错误日志。
 *
 * @param str 输入的C风格字符串。
 * @param out_val 用于存储转换成功的无符号长整型值的指针。
 * @return bool 如果转换完全成功，返回 true；否则返回 false。
 */
bool cfg_str_to_uint64(const char *str, uint64_t *out_val);

/**
 * @brief 在字符串中高效地替换所有匹配的子字符串。
 *
 * 此函数通过预先计算所需总长度，只进行一次内存分配，从而避免了
 * 循环中反复的内存重分配，性能远高于简单的循环拼接。
 *
 * @param source_str 源 sds 字符串。
 * @param pattern    要被替换的子字符串。
 * @param replacement 用来替换的新子字符串。
 *
 * @return sds  一个包含替换结果的【新】sds 字符串。
 *              如果发生错误，返回 NULL。
 *              如果源字符串或 pattern 为 NULL/空，会返回源字符串的一个副本。
 */
sds cfg_replace_str(const char *source_str, const char *pattern, const char *replacement);

/**
 * @brief 将数据完整地写入文件描述符。
 *
 * 此函数会循环调用 write 系统调用，直到所有数据都被写入为止。
 * 如果遇到错误，会记录错误日志并返回 false。
 *
 * @param fd 文件描述符。
 * @param data 要写入的数据。
 * @param size 数据的大小。
 *
 * @return bool 如果所有数据都成功写入，返回 true；否则返回 false。
 */
bool cfg_safe_write(int fd, const void *data, size_t size);

/**
 * @brief 安全地从数据库结果中获取字符串值
 *
 * @param res PostgreSQL查询结果
 * @param row 行号
 * @param col 列号
 * @return const char* 字符串值，如果为NULL则返回空字符串
 */
const char *safe_get_value(PGresult *res, size_t row, int col);

/**
 * @brief 定义并导出一个插件模块。
 *
 * 这个宏封装了插件定义的完整样板代码。它会：
 * 1. 创建一个静态的、仅在当前文件可见的插件定义结构体，避免了全局命名空间冲突。
 * 2. 创建一个全局可见的、遵循命名约定的初始化函数（如 dict_config_init）。
 * 3. 在初始化函数中，安全地将运行时上下文与插件关联并调用注册函数。
 *
 * @param module_name   插件模块的名称 (例如：dict, regu, snort)。这会自动用于生成变量和函数名。
 * @param interval      检查间隔 (check_interval)。
 * @param grace         宽限期 (grace_period)。
 * @param create_fn     插件的 create 函数指针。
 * @param destroy_fn    插件的 destroy 函数指针。
 */
#define DEFINE_PLUGIN_MODULE(module_name, interval, grace, create_fn, destroy_fn) \
    /*
    * 1. 使用 static 关键字！
    * 这会将该变量的作用域限制在当前.c文件中，解决了最关键的"multiple definition"链接错误。
    * 每个使用此宏的模块都会有自己的一份私有的 `module_name##_plugin` 实例。
    */ \
        static config_plugin_t module_name##_plugin = {\
            .name = #module_name, \
            .check_interval = interval, \
            .grace_period = grace, \
            .create = create_fn, \
            .destroy = destroy_fn, \
    }; \
        \
        /*
            * 2. 定义一个全局可见的初始化函数。
            * 函数名通过宏参数自动生成，保持了 `xxx_config_init` 的命名约定。
            */ \
        int module_name##_config_init(plugin_ctx_t *ctx) \
    { \
        /* 3. 在函数内部完成运行时状态的绑定和注册 */ \
        module_name##_plugin.user = ctx; \
        ctx->plugin_id = config_plugin_register(&module_name##_plugin); \
        return ctx->plugin_id; \
    }

#endif // __CFG_UTIL_H__
