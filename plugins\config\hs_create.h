#ifndef __HS_CREATE_H__
#define __HS_CREATE_H__

#include "cfg_util.h"

typedef base_tdata_t hs_tdata_t;

typedef struct {
    sds key;    // 特征id
    sds name;   // 特征名称
    sds value;  // 特征内容

    // --- 新增扩展属性字段 ---
    unsigned long long min_length;  // 规则的最小匹配长度 (0表示不限制)
    unsigned long long max_offset;  // 规则的最大匹配偏移 (0表示不限制)

    uint64_t type;  // 类型,标识用于标签还是风险:0.标签 1.风险
    uint64_t attrb; // 属性,标识是否需要内容提取:0.不提取 1.提取

    cvector_vector_type(pcre *) re; // pcre
} hs_item_t;

typedef struct {
    hs_database_t *database;
    cvector_vector_type(hs_scratch_t *) scratchs;
} hs_db_t;

/**
 * @brief 创建Hyperscan数据库
 * @param tdata Hyperscan tdata对象
 * @param threads_num 线程数
 * @return bool 成功返回true，失败返回false
 */
bool hs_database_create(void *arg, int threads_num);

/**
 * @brief 销毁Hyperscan数据库
 * @param tdata Hyperscan tdata对象
 */
void hs_database_destroy(void *arg);

#endif // __HS_CREATE_H__
