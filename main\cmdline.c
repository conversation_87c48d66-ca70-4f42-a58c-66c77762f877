
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>
#include <unistd.h>
#include <time.h>
#include <an_common.h>
#include "analyser.h"
#include "cmdline.h"
#include "config.h"

#define SERVER_PORT 8888
#define BUFFER_SIZE 4096
#define MAX_CLIENTS 10

#define MAX_COMMANDS 32

/* 全局变量 */
static an_cli_server_t* g_server = NULL;

/* 命令处理函数 */
static int cmd_help(int argc, char** argv, char* response, size_t resp_size, void* user_data)
{
    an_cli_command_t commands[MAX_COMMANDS];
    size_t count = MAX_COMMANDS;
    an_cmd_reg_list(an_get_cmd_reg(g_server), commands, &count);
    //an_cmd_reg_list(g_server->cmd_reg, commands, &count);

    /* 构建帮助信息 */
    char *p = response;
    size_t remain = resp_size;
    int n;

    n = snprintf(p, remain, "Available commands:\n");
    if (n < 0 || (size_t)n >= remain)
        return -1;
    p += n;
    remain -= n;

    for (size_t i = 0; i < count; i++)
    {
        n = snprintf(p, remain, "  %-10s - %s\n",
                     commands[i].name,
                     commands[i].description ? commands[i].description : "");
        if (n < 0 || (size_t)n >= remain)
            return -1;
        p += n;
        remain -= n;
    }

    n = snprintf(p, remain, "  quit     - Quit the client\n");
    if (n < 0 || (size_t)n >= remain)
        return -1;

    return 0;
}

static int cmd_echo(int argc, char** argv, char* response, size_t resp_size, void* user_data)
{
    snprintf(response, resp_size, "Echo: %s\n", argv[1]);
    return 0;
}

static int cmd_time(int argc, char** argv, char *response, size_t resp_size, void* user_data)
{
    time_t now = time(NULL);
    char *time_str = ctime(&now);
    if (time_str)
    {
        time_str[strlen(time_str) - 1] = '\0'; /* Remove newline */
        snprintf(response, resp_size, "Current time: %s\n", time_str);
    }
    else
    {
        snprintf(response, resp_size, "Failed to get time\n");
    }
    return 0;
}

static int cmd_clients(int argc, char** argv, char *response, size_t resp_size, void* user_data)
{
    size_t count = an_cli_client_count(g_server);
    snprintf(response, resp_size, "Connected clients: %zu\n", count);
    return 0;
}
command_reg_t* get_cmd_reg()
{
    return an_get_cmd_reg(g_server);
}
/* 服务器示例 */
int cmd_server_init()
{
    an_config_node_t *root_node = get_app_conf();
    an_config_node_t *node = an_config_query(root_node, "global_settings.cmdline.port");
    int port = an_config_get_int(node, SERVER_PORT);
    node = an_config_query(root_node, "global_settings.cmdline.buffer_size");
    int buffer_size = an_config_get_int(node, BUFFER_SIZE);

    /* 创建服务器配置 */
    an_cli_server_config_t config = {
        .port = port,
        .max_clients = MAX_CLIENTS,
        .buffer_size = buffer_size,
        .max_commands = MAX_COMMANDS};

    /* 创建服务器 */
    g_server = an_cli_server_create(&config);
    if (!g_server)
    {
        printf("Failed to create server\n");
        return TM_ECODE_FAILED;
    }
    /* 注册命令 */
    command_reg_t* reg = get_cmd_reg();
    an_cmd_reg_register(reg, "help", "Show help message", cmd_help, NULL);
    an_cmd_reg_register(reg, "echo", "Echo back the input", cmd_echo, NULL);
    an_cmd_reg_register(reg, "time", "Show current time", cmd_time, NULL);
    an_cmd_reg_register(reg, "clients", "Show connected clients", cmd_clients, NULL);
    return TM_ECODE_OK;
}
int cmd_server_start()
{
    /* 启动服务器 */
    //printf("Starting server on port %d...\n", SERVER_PORT);
    LOG_INFO(LOG_TAG, "Cmd Server Started, listen %d", SERVER_PORT);
    if (an_cli_server_start(g_server) != 0)
    {
        printf("Failed to start server\n");
        //an_cli_server_destroy(g_server);
        return TM_ECODE_FAILED;
    }
    return TM_ECODE_OK;
    /* 清理资源 */
    // an_cli_server_destroy(g_server);
    // g_server = NULL;
}

void cmd_server_destroy()
{
    an_cli_server_destroy(g_server);
}

void run_response(const char *host, char *param)
{
    an_config_node_t *root_node = get_app_conf();
    an_config_node_t *node = an_config_query(root_node, "global_settings.cmdline.port");
    int port = an_config_get_int(node, SERVER_PORT);
    node = an_config_query(root_node, "global_settings.cmdline.buffer_size");
    int buffer_size = an_config_get_int(node, BUFFER_SIZE);

    /* 创建客户端配置 */
    an_cli_client_config_t config = {
        .host = host ? host : "127.0.0.1",
        .port = port,
        .buffer_size = buffer_size,
        .timeout_ms = 5000};

    /* 创建客户端 */
    an_cli_client_t* client = an_cli_client_create(&config);
    if (!client)
    {
        printf("Failed to create client\n");
        return;
    }

    if (an_cli_client_connect(client) != 0)
    {
        printf("Failed to connect: %s\n", an_cli_client_last_error(client));
        an_cli_client_destroy(client);
        return;
    }

    char input[config.buffer_size];
    sprintf(input, "%s\n", param);
    char *response = NULL;
    /* 发送命令 */
    if (an_cli_send_cmd(client, input, &response) != 0)
    {
        printf("Error: %s\n", an_cli_client_last_error(client));
    } else {
        /* 显示响应 */
        printf("%s\n", response);
    }

    /* 清理资源 */
    an_cli_client_destroy(client);
}

/* 客户端示例 */
void run_client(const char *host)
{
    an_config_node_t *root_node = get_app_conf();
    an_config_node_t *node = an_config_query(root_node, "global_settings.cmdline.port");
    int port = an_config_get_int(node, SERVER_PORT);
    node = an_config_query(root_node, "global_settings.cmdline.buffer_size");
    int buffer_size = an_config_get_int(node, BUFFER_SIZE);

    /* 创建客户端配置 */
    an_cli_client_config_t config = {
        .host = host,
        .port = port,
        .buffer_size = buffer_size,
        .timeout_ms = 5000};

    /* 创建客户端 */
    an_cli_client_t* client = an_cli_client_create(&config);
    if (!client)
    {
        printf("Failed to create client\n");
        return;
    }

    /* 连接到服务器 */
    printf("Connecting to %s:%d...\n", host, SERVER_PORT);
    if (an_cli_client_connect(client) != 0)
    {
        printf("Failed to connect: %s\n", an_cli_client_last_error(client));
        an_cli_client_destroy(client);
        return;
    }

    printf("Connected to server\n");
    printf("Type 'help' for available commands\n");

    /* 主循环 */
    char input[config.buffer_size];

    while (1)
    {
        /* 读取用户输入 */
        printf("> ");
        if (!fgets(input, sizeof(input), stdin))
        {
            break;
        }

        /* 移除换行符 */
        //input[strcspn(input, "\n")] = '\0';

        /* 检查退出命令 */
        if (strncmp(input, "quit", 4) == 0)
        {
            break;
        }
        char *response;
        /* 发送命令 */
        if (an_cli_send_cmd(client, input, &response) != 0)
        {
            printf("Error: %s\n", an_cli_client_last_error(client));
            break;
        }

        /* 显示响应 */
        printf("%s\n", response);
    }

    /* 清理资源 */
    an_cli_client_destroy(client);
}

/* 主函数 */
// int main(int argc, char *argv[])
// {
//     if (argc > 1)
//     {
//         /* 客户端模式 */
//         run_client(argv[1]);
//     }
//     else
//     {
//         /* 服务器模式 */
//         cmd_server_start();
//     }
//     return 0;
// }
