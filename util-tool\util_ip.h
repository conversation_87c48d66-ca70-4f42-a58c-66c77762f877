#ifndef __UTIL_IP_H__
#define __UTIL_IP_H__

/**
 * @brief 释放IP树
 *
 * @param arg 树的指针
 */
void util_free_ip_tree(void *arg);

/**
 * @brief 添加IP树
 *
 * @param values 以COMMON_SEPARATOR分隔的IP规格字符串。
 * @param tree   目标IP树的指针。
 */
void util_add_ip_tree(const char *values, void *tree);

/**
 * @brief 匹配IP
 *
 * @param tree IP树的指针
 * @param ip  要匹配的IP
 * @return int 匹配成功返回1，失败返回0
 */
int util_match_ip(void *tree, char *ip);

#endif
