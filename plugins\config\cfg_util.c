#include "cfg_util.h"

#define CFG_UTIL_LOG_TAG "CFG_UTIL"

// 定义全局变量
cfg_conf_t *g_conf = NULL;

/**
 * @brief 执行一个SQL查询，但不关闭连接。
 *
 * @param conn_ptr 数据库连接。如果传入*conn为NULL，则会尝试新建连接。
 * @param conn_info 数据库连接信息
 * @param sql SQL查询语句
 * @return PGresult* 成功则返回结果集，失败则返回NULL。调用者负责释放结果集和关闭连接。
 */
static PGresult *cfg_execute_query(PGconn **conn_ptr, const char *conn_info, const char *sql)
{
    if (*conn_ptr == NULL) {
        *conn_ptr = pg_connect(conn_info);
        CHECK_ERR_EXEC(!*conn_ptr, CFG_UTIL_LOG_TAG, return NULL, "pg_connect error: %s", conn_info);
    }

    // 连接状态检查
    CHECK_ERR_EXEC(PQstatus(*conn_ptr) != CONNECTION_OK, CFG_UTIL_LOG_TAG,
        PQfinish(*conn_ptr); *conn_ptr = NULL; return NULL,
        "Connection failed: %s", PQerrorMessage(*conn_ptr));

    // 执行SQL语句
    PGresult *res = PQexecParams(*conn_ptr, sql, 0, NULL, NULL, NULL, NULL, 0);

    // 检查查询是否成功，注意：即使失败，res也可能不为NULL，需要用PQclear释放
    CHECK_ERR_EXEC(PQresultStatus(res) != PGRES_TUPLES_OK, CFG_UTIL_LOG_TAG,
        if (res) {
            PQclear(res);
        } return NULL,
            "Query failed: %s\nSQL: %s", PQerrorMessage(*conn_ptr), sql);

    // 成功！直接返回结果集，不关闭连接
    return res;
}

/**
 * @brief 根据模块描述符，从数据库通用地创建数据对象
 *
 * @param arg 必须是一个指向 cfg_module_create_t 的指针
 * @return void *成功返回创建的数据对象，失败返回NULL
 */
void *cfg_module_create(void *arg)
{
    const cfg_module_create_t *desc = (const cfg_module_create_t *)arg;

    // 检查核心回调是否存在
    CHECK_ERR_EXEC(!desc || !desc->create_item || !desc->destroy_item, CFG_UTIL_LOG_TAG, return NULL, "Invalid module descriptor: %p", desc);

    int success = 0;
    void *tdata = NULL;
    PGconn *conn = NULL;
    PGresult *res = NULL;

    // 1. 检查策略更新
    if (srem_redis_key(g_conf->redis_host, g_conf->redis_port, NULL, g_conf->redis_db, g_conf->redis_key, desc->redis_key) != 0) {
        return NULL;
    }

    // 2. 构造SQL
    char sql[2048] = {0};
    snprintf(sql, sizeof(sql), "SELECT * FROM %s;", desc->table_name);

    // 3. 执行SQL
    res = cfg_execute_query(&conn, g_conf->pg_conn, sql);
    CHECK_ERR_GOTO(!res, CFG_UTIL_LOG_TAG, cleanup, "SQL execution failed");

    // 4. 验证表结构和数据
    CHECK_ERR_GOTO(PQnfields(res) != desc->expect_cols, CFG_UTIL_LOG_TAG, cleanup, "%s table fields error: %d != %d", desc->table_name, PQnfields(res), desc->expect_cols);
    int rows = PQntuples(res);
    if (rows <= 0) {
        success = 1; // 空表也是一种成功
        goto cleanup;
    }

    // 5. 分配主数据结构
    tdata = calloc(1, desc->tdata_size);
    CHECK_ERR_GOTO(!tdata, CFG_UTIL_LOG_TAG, cleanup, "Memory allocation failed for tdata (size: %zu)", desc->tdata_size);

    // 6. 初始化 items 向量 (预估容量)
    base_tdata_t *base = (base_tdata_t *)tdata;
    cvector_init(base->items, 0, desc->destroy_item); // 初始大小为0，按需增长
    // TODO:预留空间以提高性能,该方法比cvector_init直接分配空间性能更好
    cvector_reserve(base->items, rows);

    // 7. 循环处理每一行：给base_tdata_t的items赋值
    for (size_t i = 0; i < rows; i++) {
        // 直接调用回调，传入完整的tdata。让回调函数自己决定创建多少个item并push_back。
        if (desc->create_item(&base->items, res, i) != CFG_SUCCESS) {
            // 如果回调失败，它应该保证没有留下“半成品”item。我们直接终止。
            goto cleanup;
        }
    }

    // 8. 可选的后处理：给base_tdata_t的ctx赋值
    if (desc->post_process && desc->post_process(tdata) != CFG_SUCCESS) {
        goto cleanup;
    }

    success = 1;

cleanup:
    // 清理逻辑
    if (res) PQclear(res);
    if (conn) pg_close(conn);

    if (success) {
        return tdata;
    } else {
        if (tdata) { // 使用模块特定的销毁器或默认销毁器
            if (desc->destroy_tdata) {
                desc->destroy_tdata(tdata);
            } else {
                base_tdata_t *b = (base_tdata_t *)tdata;
                cvector_free(b->items);
                free(tdata);
            }
        }
        return NULL;
    }
}

/**
 * @brief 在字符串中高效地替换所有匹配的子字符串。
 *
 * 此函数通过预先计算所需总长度，只进行一次内存分配，从而避免了
 * 循环中反复的内存重分配，性能远高于简单的循环拼接。
 *
 * @param source_str 源字符串 (普通的 C 字符串)。
 * @param pattern    要被替换的子字符串。
 * @param replacement 用来替换的新子字符串。
 *
 * @return sds  一个包含替换结果的【新】sds 字符串。
 *              如果发生错误，返回 NULL。
 *              如果源字符串或 pattern 为 NULL/空，会返回源字符串的一个副本。
 */
sds cfg_replace_str(const char *source_str, const char *pattern, const char *replacement)
{
    // --- 1. 参数有效性检查 ---
    CHECK_ERR_EXEC(!source_str || !pattern || !replacement, CFG_UTIL_LOG_TAG, return NULL, "Source string cannot be NULL.");

    size_t source_len = strlen(source_str);
    size_t pattern_len = (pattern == NULL) ? 0 : strlen(pattern);

    // 如果 pattern 无效或为空，则无需替换，直接返回源字符串的副本
    if (pattern_len == 0) {
        return sdsnew(source_str);
    }

    // 如果 replacement 为 NULL，视为空字符串进行替换
    if (replacement == NULL) {
        replacement = "";
    }
    size_t replacement_len = strlen(replacement);

    // --- 2. 预计算和预分配内存 ---

    // a. 计算 pattern 在源字符串中出现的次数
    size_t match_count = 0;
    const char *current = source_str;
    const char *match = NULL;
    // 只有当 pattern_len > 0 时才进行搜索
    while ((match = strstr(current, pattern)) != NULL) {
        match_count++;
        current = match + pattern_len;
    }

    // 如果没有匹配项，直接返回源字符串的副本，避免不必要的内存操作
    if (match_count == 0) {
        // [修复] 使用 sdsnew 从 const char* 创建，而不是 sdsdup
        return sdsnew(source_str);
    }

    // b. 计算结果字符串的总长度
    // 总长度 = 源长度 - 所有 pattern 的总长度 + 所有 replacement 的总长度
    size_t result_len = source_len - (match_count * pattern_len) + (match_count * replacement_len);

    // c. 创建一个足够大的 sds 字符串，只分配一次内存
    sds result = sdsnewlen(NULL, result_len);
    CHECK_ERR_EXEC(result == NULL, CFG_UTIL_LOG_TAG, return NULL, "Failed to allocate memory for result string.");

    // --- 3. 一次性填充内容 ---
    current = source_str;      // 重置 current 指针
    char *result_ptr = result; // 指向结果字符串的当前写入位置

    while ((match = strstr(current, pattern)) != NULL) {
        // a. 拷贝从 current 到 match 之间的内容 (匹配项之前的部分)
        size_t non_match_len = match - current;
        if (non_match_len > 0) {
            memcpy(result_ptr, current, non_match_len);
            result_ptr += non_match_len;
        }

        // b. 拷贝 replacement 的内容
        if (replacement_len > 0) {
            memcpy(result_ptr, replacement, replacement_len);
            result_ptr += replacement_len;
        }

        // c. 更新下一次搜索的起始位置
        current = match + pattern_len;
    }

    // --- 4. 拷贝源字符串末尾的剩余部分 ---
    // (从最后一个匹配项之后到字符串末尾)
    size_t remaining_len = source_len - (current - source_str);
    if (remaining_len > 0) {
        memcpy(result_ptr, current, remaining_len);
    }

    return result;
}

/**
 * @brief 将字符串安全地转换为整型，并记录详细错误日志。
 */
int cfg_str_to_int(const char *str)
{
    // 1. 检查无效输入
    CHECK_ERR_EXEC(str == NULL || *str == '\0', CFG_UTIL_LOG_TAG, return -1, "Invalid input: string is NULL or empty.");

    // 2. 清除 errno，准备调用 strtol
    errno = 0;
    char *endptr;

    // 3. 核心转换
    long result = strtol(str, &endptr, 10);

    // 4. 检查各种错误条件
    // a. 检查溢出
    CHECK_ERR_EXEC(errno == ERANGE || result > INT_MAX || result < INT_MIN, CFG_UTIL_LOG_TAG, return -1,
        "Conversion failed: string \"%s\" caused an overflow or underflow.", str);

    // b. 检查是否根本没有数字
    CHECK_ERR_EXEC(endptr == str, CFG_UTIL_LOG_TAG, return -1,
        "Conversion failed: string \"%s\" contains no valid digits at the beginning.", str);

    // c. 检查尾随字符
    CHECK_ERR_EXEC(*endptr != '\0', CFG_UTIL_LOG_TAG, return -1,
        "Conversion failed: string \"%s\" has non-whitespace trailing characters starting at \"%s\".", str, endptr);

    // 5. 转换成功
    return (int)result;;
}

/**
 * @brief 将字符串安全地转换为无符号长整型 (uint64_t)，并记录详细错误日志。
 *
 * 此函数封装了 strtoul，简化了API，调用者只需关心成功与否。
 * 转换失败的详细原因会被记录到日志中。
 * 此函数是线程安全的。
 *
 * @param str           输入的C风格字符串。
 * @param out_val       [输出] 用于存储转换成功的无符号长整型值的指针。
 *
 * @return bool         如果转换完全成功，返回 true；否则返回 false。
 *                      “完全成功”意味着字符串只包含数字和可选的空白符。
 */
bool cfg_str_to_uint64(const char *str, uint64_t *out_val)
{
    char *endptr;

    // 1. 检查无效输入
    CHECK_ERR_EXEC(str == NULL || *str == '\0', CFG_UTIL_LOG_TAG, return false, "Invalid input: string is NULL or empty.");

    // 2. 清除 errno，准备调用 strtoul
    errno = 0;

    // 3. 核心转换
    uint64_t result = strtoull(str, &endptr, 10);

    // 4. 检查各种错误条件
    // a. 检查溢出
    CHECK_ERR_EXEC(errno == ERANGE, CFG_UTIL_LOG_TAG, return false, "Conversion failed: string \"%s\" caused an overflow.", str);

    // b. 检查是否根本没有数字
    CHECK_ERR_EXEC(endptr == str, CFG_UTIL_LOG_TAG, return false, "Conversion failed: string \"%s\" contains no valid digits at the beginning.", str);

    // c. 检查尾随字符
    char *trailing_char = endptr;
    while (isspace((unsigned char)*trailing_char)) {
        trailing_char++;
    }

    CHECK_ERR_EXEC(*trailing_char != '\0', CFG_UTIL_LOG_TAG, return false, "Conversion failed: string \"%s\" has non-whitespace trailing characters starting at \"%s\".", str, endptr);

    // 5. 转换成功
    *out_val = result;
    return true;
}

/**
 * @brief 确保将指定大小的数据完整地写入文件描述符。
 *
 * 此函数循环调用 write() 以处理部分写入，并能正确处理被信号中断 (EINTR) 的情况。
 * 任何写入错误都会被记录到日志中。
 *
 * @param fd        目标文件描述符。
 * @param data      要写入的数据的指针。
 * @param size      要写入的数据大小（字节数）。
 *
 * @return bool     如果所有数据都成功写入，返回 true；否则返回 false。
 */
bool cfg_safe_write(int fd, const void *data, size_t size)
{
    const char *buffer = (const char *)data; // 使用 char* 进行指针运算

    // 1. 基本参数验证
    if (fd < 0 || buffer == NULL) {
        // 对于 size == 0 的情况，我们认为“成功写入了0字节”，直接返回 true
        if (size == 0) {
            return true;
        }
        // 否则，记录错误日志
        CHECK_ERR_EXEC(1, CFG_UTIL_LOG_TAG, return false, "Invalid arguments: fd=%d, data=%p", fd, (const void *)buffer);
    }

    size_t total_written = 0;
    while (total_written < size) {
        // 2. 调用 write 系统调用
        ssize_t written_this_call = write(fd, buffer + total_written, size - total_written);

        if (written_this_call < 0) {
            // 3. 错误处理
            // a. 如果是被信号中断，则忽略错误，继续循环
            if (errno == EINTR) {
                continue;
            }

            // b. 对于其他所有错误，记录日志并返回失败
            // 这里的 errno 是由 write() 设置的
            CHECK_ERR_EXEC(1, CFG_UTIL_LOG_TAG, return false, "Failed to write to fd %d after writing %zu bytes", fd, total_written);
        }

        // 4. 更新已写入的总字节数
        total_written += (size_t)written_this_call;
    }

    // 5. 所有数据都已成功写入
    return true;
}

/**
 * @brief 安全地从数据库结果中获取字符串值
 *
 * @param res PostgreSQL查询结果
 * @param row 行号
 * @param col 列号
 * @return const char* 字符串值，如果为NULL则返回空字符串
 */
const char *safe_get_value(PGresult *res, size_t row, int col)
{
    const char *value = PQgetvalue(res, row, col);
    return value ? value : "";
}
