#include <an_calc.h>
#include <db_proc_dict.h>

#define LOG_TAG "oper_dict"

typedef struct {
    int data_index;
    int result_index;
    const dict_item_t *item;
} oper_dict_ctx_t;

static const char *const LOGICAL_NAME_DICT_INPUT = "dict_input";
static const char *const LOGICAL_NAME_DICT_OUTPUT = "dict_output";

static const an_operator_variable_req_t dict_dependencies[] = {
    {LOGICAL_NAME_DICT_INPUT, AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN} // Terminator
};

static const an_operator_variable_req_t dict_outputs[] = {
    {LOGICAL_NAME_DICT_OUTPUT, AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN} // Terminator
};

static const an_operator_variable_req_t *dict_get_deps()
{
    return dict_dependencies;
}

static const an_operator_variable_req_t *dict_get_outs()
{
    return dict_dependencies;
}

static void oper_dict_reset(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    oper_dict_ctx_t *ctx = (oper_dict_ctx_t *)oper_ctx->user;


}

static double oper_dict_execute(an_calc_session_t *session, an_calc_oper_ctx_t *oper_ctx)
{
    CHECK_ERR_EXEC(!session || !oper_ctx || !oper_ctx->user || oper_ctx->thread_id < 0, LOG_TAG, return 0.0, "");

    oper_dict_ctx_t *ctx = (oper_dict_ctx_t *)oper_ctx->user;
    CHECK_ERR_EXEC(ctx->data_index < 0 || ctx->result_index < 0, LOG_TAG, return 0.0, "Invalid ctx: %p", ctx);

    char *data = an_calc_get_string(session, ctx->data_index);
    CHECK_ERR_EXEC(!data || strlen(data) <= 0, LOG_TAG, return 0.0, "Invalid data: %s", data);;

    ctx->item = dict_item_get_by_value(data);
    CHECK_ERR_EXEC(!ctx->item, LOG_TAG, return 0.0, "Invalid item: %s", data);

    an_calc_set_pointer(session, ctx->result_index, (void *)ctx->item);

    return 100.0;
}

static int oper_dict_init(an_calc_oper_ctx_t *oper_ctx)
{
    oper_dict_ctx_t *ctx = (oper_dict_ctx_t *)calloc(1, sizeof(oper_dict_ctx_t));
    if (!ctx) {
        return -1;
    }

    ctx->data_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_DICT_INPUT);
    ctx->result_index = oper_ctx->get_output_index(oper_ctx, LOGICAL_NAME_DICT_OUTPUT);

    if (ctx->data_index < 0 || ctx->result_index < 0) {
        free(ctx);
        return -1;
    }

    oper_ctx->user = ctx;

    return 0;
}

static void oper_dict_cleanup(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    oper_dict_reset(oper_ctx);

    free(oper_ctx->user);
    oper_ctx->user = NULL;
}

an_operator_plugin_t oper_dict = {
    .name = "oper_dict",
    .version = "1.0.0",
    .get_dependencies = dict_get_deps,
    .get_outputs = dict_get_outs,
    .init = oper_dict_init,
    .fini = oper_dict_cleanup,
    .execute = oper_dict_execute,
    .reset = oper_dict_reset
};
