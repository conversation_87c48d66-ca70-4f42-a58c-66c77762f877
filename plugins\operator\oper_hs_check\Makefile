include $(DSAP_BUILD_MACROS_MK)

#$(error $(shell pkg-config --cflags libxlib) )
CFLAGS_LOCAL += $(shell pkg-config --cflags libxlib libhs)
CFLAGS_LOCAL += -I$(INSTALL_ROOT_INCLUDE)/an_util -I$(INSTALL_ROOT_INCLUDE)/an_calc
CFLAGS_LOCAL += -I$(INSTALL_ROOT_INCLUDE)/an_cfg_svc -I $(INSTALL_ROOT_INCLUDE)/util_tool
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/cfg_svc

LDFLAGS += $(shell pkg-config --libs libhs)

BUILD_TYPE = dynlib
BUILD_NAME = oper_hs_check
BUILD_VERSION = 1
INSTALL_APEEND_PATH = an_plugin
BUILD_DYNLIB_PGKCONFIG = 1

INCLUDE_FILES +=

include $(DSAP_BUILD_RULES_MK)
