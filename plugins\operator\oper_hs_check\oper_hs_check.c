#include <an_calc.h>
#include <hs_match.h>
#include <hs_create.h>
#include <an_cfg_svc.h>
#include <db_proc_regu.h>

#define HS_CHECK_LOG_TAG "oper_hs_check"

static const char *const LOGICAL_NAME_HS_CHECK_SOURCE = "hs_check_source";
static const char *const LOGICAL_NAME_HS_CHECK_TARGET = "hs_check_target";
static const char *const LOGICAL_NAME_HS_CHECK_RESULT = "hs_check_result";

typedef struct {
    int check_source_index;
    int check_target_index;
    int check_result_index;
} hs_check_ctx_t;

static const an_operator_variable_req_t hs_check_dependencies[] = {
    {LOGICAL_NAME_HS_CHECK_SOURCE, AN_VAR_TYPE_POINTER},
    {LOGICAL_NAME_HS_CHECK_TARGET, AN_VAR_TYPE_STRING},
    {NULL, AN_VAR_TYPE_UNKNOWN}
};

static const an_operator_variable_req_t hs_check_outputs[] = {
    {LOGICAL_NAME_HS_CHECK_RESULT, AN_VAR_TYPE_DOUBLE},
    {NULL, AN_VAR_TYPE_UNKNOWN}
};

static const an_operator_variable_req_t *hs_check_get_deps(void)
{
    return hs_check_dependencies;
}

static const an_operator_variable_req_t *hs_check_get_outs(void)
{
    return hs_check_outputs;
}

/**
 * @brief 重置函数 - 清理所有匹配结果
 */
static void hs_check_reset(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    hs_check_ctx_t *ctx = (hs_check_ctx_t *)oper_ctx->user;
}

/**
 * @brief 执行函数 - 主要业务逻辑
 */
static double hs_check_execute(an_calc_session_t *session, an_calc_oper_ctx_t *oper_ctx)
{
    CHECK_ERR_EXEC(!session || !oper_ctx || !oper_ctx->user || oper_ctx->thread_id < 0, HS_CHECK_LOG_TAG, return 0.0, "Invalid parameters");

    hs_check_ctx_t *ctx = (hs_check_ctx_t *)oper_ctx->user;

    void *check_source = an_calc_get_pointer(session, ctx->check_source_index);
    char *check_target = an_calc_get_string(session, ctx->check_target_index);

    hs_result_t *source = (hs_result_t *)check_source;

    if (!source || !source->match_items || cvector_size(source->match_items) <= 0 || !check_target) {
        return 0.0;
    }

    for (size_t i = 0; i < cvector_size(source->match_items); i++) {
        match_item_t *item = source->match_items[i];
        if (!item || !item->key) {
            continue;
        }

        if (strcmp(item->key, check_target) == 0) {
            an_calc_set_double(session, ctx->check_result_index, (double)100.0);
            return (double)100.0;
        }
    }

    an_calc_set_double(session, ctx->check_result_index, (double)0.0);
    return 0.0;
}

/**
 * @brief 初始化函数
 */
static int hs_check_init(an_calc_oper_ctx_t *oper_ctx)
{
    hs_check_ctx_t *ctx = (hs_check_ctx_t *)calloc(1, sizeof(hs_check_ctx_t));
    if (!ctx) {
        LOG_ERROR(HS_CHECK_LOG_TAG, "Failed to allocate memory for context");
        return -1;
    }

    ctx->check_source_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_HS_CHECK_SOURCE);
    ctx->check_target_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_HS_CHECK_TARGET);

    if (ctx->check_source_index < 0 || ctx->check_target_index < 0) {
        LOG_ERROR(HS_CHECK_LOG_TAG, "Failed to resolve dependencies");
        free(ctx);
        return -1;
    }

    oper_ctx->user = ctx;

    LOG_INFO(HS_CHECK_LOG_TAG, "oper_hs_check initialized successfully");
    return 0;
}

/**
 * @brief 清理函数
 */
static void hs_check_cleanup(an_calc_oper_ctx_t *oper_ctx)
{
    if (!oper_ctx || !oper_ctx->user) {
        return;
    }

    // 清理所有结果
    hs_check_reset(oper_ctx);

    // 释放上下文
    free(oper_ctx->user);
    oper_ctx->user = NULL;

    LOG_INFO(HS_CHECK_LOG_TAG, "oper_hs_check cleaned up");
}

// 算子插件定义
an_operator_plugin_t oper_hs_check = {
    .name = "oper_hs_check",
    .version = "1.0.0",
    .get_dependencies = hs_check_get_deps,
    .get_outputs = hs_check_get_outs,
    .init = hs_check_init,
    .fini = hs_check_cleanup,
    .execute = hs_check_execute,
    .reset = hs_check_reset
};
