include $(DSAP_BUILD_MACROS_MK)

#$(error $(shell pkg-config --cflags libxlib) )
CFLAGS_LOCAL += $(shell pkg-config --cflags libxlib libip2lookup)
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/an_util -I $(INSTALL_ROOT_INCLUDE)/an_calc -I $(INSTALL_ROOT_INCLUDE)/an_cfg_svc
CFLAGS_LOCAL += -I $(INSTALL_ROOT_INCLUDE)/util_tool -I $(INSTALL_ROOT_INCLUDE)/cfg_svc

LDFLAGS += $(shell pkg-config --libs libxlib libip2lookup)
LDFLAGS += -L$(INSTALL_ROOT_LIB) -lan_util -lan_cfg_svc -lcfg_svc -lutil_tool -lstdc++ -ldl

BUILD_TYPE = dynlib
BUILD_NAME = oper_business
BUILD_VERSION = 1
INSTALL_APEEND_PATH =
BUILD_DYNLIB_PGKCONFIG = 1

INCLUDE_FILES +=

include $(DSAP_BUILD_RULES_MK)
