#ifndef __OPER_MAP_H__
#define __OPER_MAP_H__

#include <khash.h>
#include <sds.h>

// 算子映射关系
typedef struct {
    sds operator_id;    // 算子ID
    sds operator_name;  // 算子名称
    sds operator_type;  // 算子类型
    sds input_vars;     // 输入变量列表
    sds output_vars;    // 输出变量列表
} oper_map_t;

// 创建算子ID到算子名称的哈希表
KHASH_MAP_INIT_STR(operator_map, oper_map_t *)

/**
 * @brief 初始化算子映射管理器
 * @return int 成功返回0，失败返回-1
 */
int init_operator_map(void);

/**
 * @brief 销毁算子映射管理器
 */
void destroy_operator_map(void);

/**
 * @brief 添加算子映射
 * @param oper_map 算子映射关系
 * @return int 成功返回0，失败返回-1
 */
int add_operator_map(oper_map_t *oper_map);

/**
 * @brief 根据算子ID获取算子名称
 * @param oper_id 算子ID
 * @return const char* 算子名称，未找到返回NULL
 */
const char *get_operator_name_by_id(const char *oper_id);

/**
 * @brief 根据算子ID获取算子映射关系
 * @param oper_id 算子ID
 * @return oper_map_t* 算子映射关系，未找到返回NULL
 */
oper_map_t *get_operator_item_by_id(const char *oper_id);

/**
 * @brief 转换表达式中的算子ID为输出变量名
 * @param expr 原始表达式
 * @return sds 转换后的表达式，需要调用者释放
 */
sds convert_expr_operator(const char *expr);

/**
 * @brief 遍历哈希表里的所有数据，并打印
 * @param void
 * @return void
 */
void print_operator_map(void);

#endif // __OPER_MAP_H__
