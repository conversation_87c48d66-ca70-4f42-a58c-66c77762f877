#include <an_common.h>
// #include <perf_ring.h>
// #include <an_expr.h>
static void register_unit_tests()
{
    //register_queue_tests();
    // register_expr_tests();
}

void run_unit_tests(int list_unittests, const char *regex_arg)
{
    UtInitialize();
    if (regex_arg == NULL)
    {
        regex_arg = ".*";
        UtRunSelftest(regex_arg); /* inits and cleans up again */
    }

    register_unit_tests();
    if (list_unittests)
    {
        UtListTests(regex_arg);
    }
    else
    {
        uint32_t failed = UtRunTests(regex_arg);
        UtCleanup();

         if (failed) {
            exit(EXIT_FAILURE);
        }
    }
    exit(EXIT_SUCCESS);
}
