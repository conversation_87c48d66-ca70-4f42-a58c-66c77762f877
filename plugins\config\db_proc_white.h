#ifndef __DB_PROC_WHITE_H__
#define __DB_PROC_WHITE_H__

#include "cfg_util.h"
#include "ip2lookup.h"

typedef base_tdata_t white_tdata_t;

typedef struct {
    sds key;
    sds value;
    int type;   // 1 ip, 2 app, 3 api, 4 domain
} white_item_t;

// 创建一个api的hash表
KHASH_MAP_INIT_STR(api_hash, sds)

// 创建一个应用的hash表
KHASH_MAP_INIT_STR(app_hash, sds)

typedef struct {
    khash_t(api_hash) *api_hash;
    khash_t(app_hash) *app_hash;
#ifdef RADIX_IP
    radix_tree_t *ip_tree;
#else
    ip2lookup_t *ip_tree;
#endif
} white_db_t;

int white_config_init(void);

int get_white_plugin_id(void);

#endif // __DB_PROC_WHITE_H__
