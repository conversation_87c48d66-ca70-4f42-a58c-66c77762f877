#ifndef __UTIL_TOOL_H__
#define __UTIL_TOOL_H__

// 静态内联函数，用于安全地取两个size_t值的较小者
inline size_t min_size(size_t a, size_t b)
{
    return a < b ? a : b;
}

// 静态内联函数，用于安全地取两个uint64_t值的较大/较小者
inline uint64_t max_u64(uint64_t a, uint64_t b)
{
    return a > b ? a : b;
}

inline uint64_t min_u64(uint64_t a, uint64_t b)
{
    return a < b ? a : b;
}

/**
 * @brief 从字符串中提取数字到数组
 * @param str 输入字符串
 * @param numbers 输出数组
 * @param max_count 数组最大容量
 * @return int 成功提取的数字个数，失败返回-1
 *
 * @note 这个版本针对性能优化，适合处理大量数据
 * @note 使用手工解析，避免strtol的开销
 * @note 溢出检查相对简单，适用于大多数场景
 */
int get_id_from_str(const char *str, long *numbers, int max_count);

int get_key_from_str_sds(const char *str, cvector_vector_type(sds) *keys);

#endif // __UTIL_TOOL_H__