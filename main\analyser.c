#include <stdio.h>
#include <getopt.h>
#include <unistd.h>
#include <string.h>

#include <an_cfg_svc.h>
#include <an_signal.h>
#include <an_time_util.h>
#include <cvector.h>
#include "unittests.h"
#include "analyser.h"
#include "cmdline.h"
#include "pipeline.h"
#include "config.h"
#include "cfg_init.h"

#include "an_string.h"
#define CFG_LOG "risk.conf"
#define CFG_FILE "risk.yml"
extern int test_main(void);

ANInstance analyser;

ANInstance *get_app_instance()
{
    return &analyser;
}

static int init_global(ANInstance *ins)
{
    int rc;
    if (load_config(ins->config_file) == false) {
        printf("ERROR: Failed to load config file %s\n", ins->config_file);
        return TM_ECODE_FAILED;
    }

    an_config_node_t * config_root = get_app_conf();
    const char *log_path = CFG_LOG;
    an_config_node_t *log_node = an_config_query(config_root, "global_settings.log_conf_path");
    if (log_node) {
        log_path = an_config_get_string(log_node, CFG_LOG);
    }

    rc = an_logger_init(log_path);
    if (rc)
    {
        return TM_ECODE_FAILED;
    }
    register_signal_handler();

    cfg_ctx_t ctx = {
        .cfg_yaml_path = "policy/cfg.yaml",
        .thread_num = 1
    };
    cfg_init(&ctx);

    return  TM_ECODE_OK;
}

static void global_destroy(void)
{
    free_config();
    an_logger_destroy();
}
static void print_help(const char *name)
{
    printf("Usage: %s [OPTIONS]\n", name);
    printf("\t-u                                   : run the unittests and exit\n");
}
static TmEcode parse_cmd_line(int argc, char *argv[], ANInstance *an)
{
    int opt;
    struct option long_opts[] = {
        {"help", no_argument, NULL, 'h'},
        {"version", no_argument, NULL, 'v'},
        {"config", required_argument, NULL, 'c'},
        {NULL, 0, NULL, 0}
    };
    int option_index = 0;
    char short_opts[] = "hvdc:uU:i:p:";
    while ((opt = getopt_long(argc, argv, short_opts, long_opts, &option_index)) != -1) {
        switch (opt)
        {
        case 0://处理长选项
            /* code */
            if(strcmp((long_opts[option_index]).name, "list-unittests") == 0) {
#ifdef UNITTESTS
                an->run_mode = RUNMODE_LIST_UNITTEST;
#else
                printf("unit tests not enabled. Make sure to pass --enable-unittests to "
                           "configure when building");
                return TM_ECODE_FAILED;
#endif /* UNITTESTS */
            }
            break;
        case 'h':
            /* code */
            break;
        case 'u':
#ifdef UNITTESTS
            if (an->run_mode == RUNMODE_NONE) {
                an->run_mode = RUNMODE_UNITEST;
            } else {
                print_help(an->name);
            }
#endif
        break;
        case 'U':
#ifdef UNITTESTS
            an->regex_arg = optarg;

            if(strlen(an->regex_arg) == 0)
                an->regex_arg = NULL;
#endif
            break;
        case 'c':
            an->config_file = optarg;
            break;
        case 'i':
            if (an->run_mode == RUNMODE_NONE)
            {
                an->run_mode = RUNMODE_CMDLINE;
                run_client(optarg);
            }
            break;
        case 'p':
            if (an->run_mode == RUNMODE_NONE)
            {
                an->run_mode = RUNMODE_CMDLINE;
                run_response(NULL, optarg);
            }
            break;
        default:
            LOG_INFO(LOG_TAG, "Unknown option: %c, exit", opt);
            an->run_mode = RUNMODE_UNKNOWN;
            return TM_ECODE_FAILED;
        }
    }
}
static void instance_init(ANInstance *an, const char *name)
{
    memset(an, 0, sizeof(ANInstance));
    an->run_mode = RUNMODE_NONE;
    an->daemon = 0;
    an->name = name;
    an->regex_arg = NULL;
    an->config_log = CFG_LOG;
    an->config_file = CFG_FILE;
}

static int start_runmode(ANInstance *an)
{
    switch (an->run_mode)
    {
    case RUNMODE_UNITEST:
        run_unit_tests(0, an->regex_arg);
        break;
    case RUNMODE_LIST_UNITTEST:
        run_unit_tests(1, an->regex_arg);
        break;
    case RUNMODE_CMDLINE:
        exit(EXIT_SUCCESS);
        break;
    case RUNMODE_UNKNOWN:
        exit(EXIT_FAILURE);
    default:
        break;
    }
}
#if 0
int test_bloc_string() {
    printf("--- Creating perf_mempool ---\n");
    #define MEMPOOL_SIZE         1000  // 内存池中对象的总数
    #define MEMBLOCK_SIZE        2048   // 每个对象的大小 (模拟一个MTU大小的数据包)
    #define MEMPOOL_CACHE_SIZE   256    // 每个线程本地缓存的大小

    perf_mempool_t *pool = perf_mempool_create("string_pool",
                                        MEMPOOL_SIZE,
                                        MEMBLOCK_SIZE,
                                        MEMPOOL_CACHE_SIZE,
                                        0, // 0 表示 MP/MC 安全
                                        NULL,
                                        NULL);

    if (!pool) {
        fprintf(stderr, "Failed to create mempool.\n");
        return 1;
    }
    printf("Mempool created. Initial available blocks: %u\n", perf_mempool_avail_count(pool));

    // Create the arena, linking it to our pool
    an_string_t* arena = an_string_create(pool);
    if (!arena) {
        fprintf(stderr, "Failed to create string arena.\n");
        perf_mempool_destroy(pool);
        return 1;
    }

    printf("\n--- Processing Cycle 1 ---\n");
    {
        // Allocate several strings.
        // We don't need to track the pointers if we just use them in this scope.
        char* s1 = an_string_alloc_str(arena, "Hello, this is the first short string.");

        // Create a large string to force a new block allocation
        char large_string_a[1001];
        memset(large_string_a, 'A', 1000);
        large_string_a[1000] = '\0';
        char* s2 = an_string_alloc_str(arena, large_string_a);

        char* s3 = an_string_alloc_str(arena, "Another string to test allocation.");

        char large_string_b[3501];
        memset(large_string_b, 'B', 3500);
        large_string_b[3500] = '\0';
        char* s4 = an_string_alloc_str(arena, large_string_b); // This will definitely need a new block

        char* s5 = an_string_alloc_str(arena, "This string should be in the second block.");

        printf("Allocated 5 strings in cycle 1:\n");
        if (s1) printf("  - \"%.30s...\"\n", s1);
        if (s2) printf("  - \"%.30s...\"\n", s2);
        if (s3) printf("  - \"%.30s...\"\n", s3);
        if (s4) printf("  - \"%.30s...\"\n", s4);
        if (s5) printf("  - \"%.30s...\"\n", s5);

        printf("Available blocks after allocation: %u\n", perf_mempool_avail_count(pool));

        printf("Releasing all blocks from cycle 1...\n");
        an_string_release_all(arena);

        printf("Available blocks after release: %u\n", perf_mempool_avail_count(pool));
        // All pointers (s1, s2, s3, s4, s5) are now invalid!
    }

    printf("\n--- Processing Cycle 2 ---\n");
    {
        char* str1 = an_string_alloc_str(arena, "This is a new cycle.");
        char* str2 = an_string_alloc_str(arena, "Allocation starts fresh.");

        printf("Allocated strings in cycle 2:\n");
        if (str1) printf("  - \"%s\"\n", str1);
        if (str2) printf("  - \"%s\"\n", str2);

        printf("Available blocks after allocation: %u\n", perf_mempool_avail_count(pool));

        printf("Releasing all blocks from cycle 2...\n");
        an_string_release_all(arena);

        printf("Available blocks after release: %u\n", perf_mempool_avail_count(pool));
    }

    printf("\n--- Destroying resources ---\n");
    an_string_destroy(arena);
    perf_mempool_destroy(pool);

    return 0;
}
#endif

int main(int argc, char *argv[])
{
    instance_init(&analyser, argv[0]);
    // 解析命令行参数
    parse_cmd_line(argc, argv, &analyser);

    start_runmode(&analyser);
    if (init_global(&analyser))
        return -1;

    //配置中心初始化
    if (an_cfg_mgr_init())
        goto stop;

    //启动成功
    g_running = true;
    // test_bloc_string();
    //配置中心启动
    an_cfg_mgr_start(0);
    an_time_svc_start(0);

    //初始化命令行
    if (cmd_server_init()) {
        goto stop;
    }

    if(pipeline_app_start()) {
        goto stop;
    }

    //loop
    //启动命令行服务
    if (cmd_server_start()) {
        goto stop;
    }

    //停止命令行服务
stop:
    g_running = false;
    cmd_server_destroy();
    pipeline_app_stop();
    an_cfg_mgr_stop();
    an_time_svc_stop();
    // 停止读取器
    //zmq_stop_multithreaded_reader(threads, thread_count);
    //zmq_wrapper_cleanup();

cleanup_global:
    global_destroy();
    printf("Shutdown complete.\n");
    return 0;
}
