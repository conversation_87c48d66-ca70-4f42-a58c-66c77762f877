#ifndef __UTIL_PGSQL_H__
#define __UTIL_PGSQL_H__

#include <libpq-fe.h>

/**
 * @brief 连接数据库
 * @param conn_str 数据库连接字符串
 * @return 数据库连接
 */
PGconn *pg_connect(const char *conn_str);

/**
 * @brief 断开数据库连接
 * @param conn 数据库连接
 */
void pg_close(PGconn *conn);

/**
 * @brief 定义一个函数指针类型，用于处理 PGresult。
 * @param res 查询结果集。
 * @param userData 用户自定义的、需要传递给回调的任意数据。
 * @return int 0表示处理成功，非0表示处理失败。
 */
typedef int (*PGResultCallback)(PGresult *res, void *userData);

/**
 * @brief 执行非查询类命令 (INSERT, UPDATE, DELETE) 并可选地调用回调。
 * @param conn 一个有效的数据库连接。
 * @param command SQL 命令模板 ($1, $2, ...)。
 * @param nParams 参数数量。
 * @param paramValues 参数值的字符串数组。
 * @param callback [可选] 命令成功后调用的回调函数，可为 NULL。
 * @param userData [可选] 传递给回调函数的自定义数据。
 * @return 0 表示成功，-1 表示失败。
 */
int pg_execute_command(PGconn *conn, const char *command, int nParams, const char *const *paramValues,
                         PGResultCallback callback, void *userData);

/**
 * @brief 执行查询 (SELECT) 并通过回调函数处理结果。
 * @param conn 一个有效的数据库连接。
 * @param query SQL 查询模板 ($1, $2, ...)。
 * @param nParams 参数数量。
 * @param paramValues 参数值的字符串数组。
 * @param callback [必须] 用于处理结果集的回调函数，不能为 NULL。
 * @param userData [可选] 传递给回调函数的自定义数据。
 * @return 0 表示成功，-1 表示失败。如果回调函数返回非0，此函数也返回-1。
 */
int pg_execute_query(PGconn *conn, const char *query, int nParams, const char *const *paramValues,
                       PGResultCallback callback, void *userData);

/**
 * @brief 打印结果集
 * @param res 结果集
 */
void pg_print_result(const PGresult *res);

#endif // __UTIL_PGSQL_H__