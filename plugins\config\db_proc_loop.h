#ifndef __DB_PROC_LOOP_H__
#define __DB_PROC_LOOP_H__

typedef base_tdata_t loop_tdata_t;

typedef struct {
    sds key;    // 漏洞ID
    sds name;   // 漏洞名称
    sds expr;   // 漏洞表达式
    sds level;  // 漏洞等级
    int type;   // 漏洞类型
    int state;  // 漏洞启用状态
} loop_item_t;

/**
 * @brief 初始化协议解析配置
 *
 * @return int 成功返回插件ID，失败返回负值
 */
int loop_config_init(void);

/**
 * @brief 获取协议解析插件ID
 *
 * @return int 插件ID，失败返回-1
 */
int get_loop_plugin_id(void);

#endif // __DB_PROC_LOOP_H__
