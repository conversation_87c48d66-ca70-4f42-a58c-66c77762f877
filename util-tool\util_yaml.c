#include <stdio.h>
#include <string.h>
#include <strings.h>
#include <stdlib.h>
#include <limits.h>
#include <errno.h>
#include "yaml.h" 
#include "util_yaml.h" 

#define MAX_KEY_LEN 256
#define MAX_DEPTH 10

// 内部解析器状态，现在是完全通用的
typedef struct {
    char path_stack[MAX_KEY_LEN * MAX_DEPTH];
    char last_key[MAX_KEY_LEN];
    int level;
    int expect_value;

    // 指向用户传入的数据和映射表
    void *user_data;
    const cb_config_map_t *map;
} parser_state_t;

/**
 * @brief 应用默认值
 *
 * @param map 字段映射表
 * @param user_struct 用户结构体
 */
static void apply_default_values(const cb_config_map_t *map, void *user_struct)
{
    for (size_t i = 0; map[i].yaml_path != NULL; ++i) {
        void *field_ptr = (char *)user_struct + map[i].offset;

        switch (map[i].type) {
        case YAML_TYPE_STRING:
            // 如果默认值是NULL，则填充空字符串，防止解引用NULL
            if (map[i].default_value.s) {
                snprintf((char *)field_ptr, map[i].size, "%s", map[i].default_value.s);
            } else {
                *((char *)field_ptr) = '\0';
            }
            break;
        case YAML_TYPE_INT:
            *(int *)field_ptr = map[i].default_value.i;
            break;
        case YAML_TYPE_LONG:
            *(long *)field_ptr = map[i].default_value.l;
            break;
        case YAML_TYPE_DOUBLE:
            *(double *)field_ptr = map[i].default_value.d;
            break;
        case YAML_TYPE_BOOL:
            *(bool *)field_ptr = map[i].default_value.b;
            break;
        }
    }
}

/**
 * @brief 设置配置值
 *
 * @param state 解析器状态
 * @param full_key 完整键
 * @param value_str 值字符串
 */
static void set_config_value(parser_state_t *state, const char *full_key, const char *value_str)
{
    for (size_t i = 0; state->map[i].yaml_path != NULL; ++i) {
        if (strcmp(state->map[i].yaml_path, full_key) == 0) {
            // 使用 void* 和 offset 计算出目标字段的内存地址
            void *field_ptr = (char *)state->user_data + state->map[i].offset;
            char *endptr;
            errno = 0;

            switch (state->map[i].type) {
            case YAML_TYPE_STRING:
                snprintf((char *)field_ptr, state->map[i].size, "%s", value_str);
                break;
            case YAML_TYPE_INT:
            case YAML_TYPE_LONG: {
                long val = strtol(value_str, &endptr, 10);
                if (endptr == value_str || *endptr != '\0' || errno == ERANGE) {
                    fprintf(stderr, "Warning: Invalid integer/long value '%s' for key '%s'. Using 0.\n", value_str, full_key);
                    val = 0;
                }
                if (state->map[i].type == YAML_TYPE_INT) {
                    if (val < INT_MIN || val > INT_MAX) {
                        fprintf(stderr, "Warning: Integer value '%ld' for key '%s' is out of range. Using INT_MAX.\n", val, full_key);
                        val = INT_MIN;
                    }
                    *(int *)field_ptr = (int)val;
                } else {
                    if (val < LONG_MIN || val > LONG_MAX) {
                        fprintf(stderr, "Warning: Long value '%ld' for key '%s' is out of range. Using LONG_MAX.\n", val, full_key);
                        val = LONG_MIN;
                    }
                    *(long *)field_ptr = val;
                }
                break;
            }
            case YAML_TYPE_DOUBLE: {
                double val = strtod(value_str, &endptr);
                if (endptr == value_str || *endptr != '\0' || errno == ERANGE) {
                    fprintf(stderr, "Warning: Invalid double value '%s' for key '%s'. Using 0.0.\n", value_str, full_key);
                    val = 0.0;
                }
                *(double *)field_ptr = val;
                break;
            }
            case YAML_TYPE_BOOL: {
                // 支持 "true", "yes", "1" (不区分大小写)
                if (strcasecmp(value_str, "true") == 0 || strcasecmp(value_str, "yes") == 0 || strcmp(value_str, "1") == 0) {
                    *(bool *)field_ptr = true;
                } else {
                    *(bool *)field_ptr = false;
                }
                break;
            }
            }
            return; // 找到并设置后即可返回
        }
    }
}

/**
 * @brief 处理映射开始事件
 *
 * @param state 解析器状态
 */
static void handle_mapping_start(parser_state_t *state)
{
    if (state->last_key[0] != '\0') {
        size_t current_len = strlen(state->path_stack);
        if (current_len > 0) {
            snprintf(state->path_stack + current_len, sizeof(state->path_stack) - current_len, ".%s", state->last_key);
        } else {
            snprintf(state->path_stack, sizeof(state->path_stack), "%s", state->last_key);
        }
        state->last_key[0] = '\0';
    }
    state->level++;
    state->expect_value = 0;
}

/**
 * @brief 处理映射结束事件
 *
 * @param state 解析器状态
 */
static void handle_mapping_end(parser_state_t *state)
{
    state->level--;
    if (state->path_stack[0] != '\0') {
        char *last_dot = strrchr(state->path_stack, '.');
        if (last_dot) {
            *last_dot = '\0';
        } else {
            state->path_stack[0] = '\0';
        }
    }
}

/**
 * @brief 处理标量事件
 *
 * @param state 解析器状态
 * @param value 标量值
 */
static void handle_scalar(parser_state_t *state, const char *value)
{
    if (!state->expect_value) {
        snprintf(state->last_key, sizeof(state->last_key), "%s", value);
        state->expect_value = 1;
    } else {
        char full_key[sizeof(state->path_stack)];
        if (state->path_stack[0] != '\0') {
            snprintf(full_key, sizeof(full_key), "%s.%s", state->path_stack, state->last_key);
        } else {
            snprintf(full_key, sizeof(full_key), "%s", state->last_key);
        }
        set_config_value(state, full_key, value);
        state->last_key[0] = '\0';
        state->expect_value = 0;
    }
}

/**
 * @brief 打印配置帮助（包含当前值）
 *
 * @param map 字段映射表
 * @param user_struct 用户结构体
 */
void yaml_config_print_help(const cb_config_map_t *map, const void *user_struct)
{
    if (!map || !user_struct) {
        return;
    }

    printf("Current Configuration Values:\n");
    printf("----------------------------------------------------------------------\n");
    printf("%-30s | %-10s | %-20s | %s\n", "YAML Path", "Type", "Current Value", "Description");
    printf("----------------------------------------------------------------------\n");

    for (size_t i = 0; map[i].yaml_path != NULL; ++i) {
        const char *type_str;
        char value_str[64] = {0};

        // 获取字段指针
        const void *field_ptr = (const char *)user_struct + map[i].offset;

        switch (map[i].type) {
        case YAML_TYPE_STRING:
            type_str = "String";
            snprintf(value_str, sizeof(value_str), "\"%s\"", (const char *)field_ptr);
            break;
        case YAML_TYPE_INT:
            type_str = "Integer";
            snprintf(value_str, sizeof(value_str), "%d", *(const int *)field_ptr);
            break;
        case YAML_TYPE_LONG:
            type_str = "Long";
            snprintf(value_str, sizeof(value_str), "%ld", *(const long *)field_ptr);
            break;
        case YAML_TYPE_DOUBLE:
            type_str = "Double";
            snprintf(value_str, sizeof(value_str), "%.2f", *(const double *)field_ptr);
            break;
        case YAML_TYPE_BOOL:
            type_str = "Boolean";
            snprintf(value_str, sizeof(value_str), "%s", *(const bool *)field_ptr ? "true" : "false");
            break;
        default:
            type_str = "Unknown";
            snprintf(value_str, sizeof(value_str), "N/A");
            break;
        }

        // 如果描述为空，则打印一个占位符
        const char *desc = map[i].description ? map[i].description : "(No description)";

        printf("%-30s | %-10s | %-20s | %s\n", map[i].yaml_path, type_str, value_str, desc);
    }
    printf("----------------------------------------------------------------------\n");
}

/**
 * @brief 公共API函数实现
 *
 * @param filepath YAML文件路径
 * @param map 字段映射表
 * @param user_struct 用户结构体
 */
int yaml_config_parse(const char *filepath, const cb_config_map_t *map, void *user_struct)
{
    if (!filepath || !map || !user_struct) {
        return -1;
    }

    // 应用默认值
    apply_default_values(map, user_struct);

    // 打开YAML文件
    FILE *fp = fopen(filepath, "r");
    if (!fp) {
        fprintf(stderr, "Info: Config file '%s' not found or cannot be opened. Using default values.\n", filepath);
        return 0; // 返回成功，因为我们已经成功应用了默认值
    }

    // 初始化状态，将用户数据和映射表传入
    parser_state_t state = {
        .user_data = user_struct,
        .map = map,
        .level = 0,
        .expect_value = 0,
        .last_key = {0},
        .path_stack = {0}
    };

    int ret = 0;
    yaml_parser_t parser;
    yaml_event_t event;

    if (!yaml_parser_initialize(&parser)) {
        fprintf(stderr, "Error: Failed to initialize YAML parser.\n");
        fclose(fp);
        return -3;
    }
    yaml_parser_set_input_file(&parser, fp);

    int done = 0;
    while (!done) {
        if (!yaml_parser_parse(&parser, &event)) {
            fprintf(stderr, "Parser error %d: %s at line %zu, column %zu\n",
                    parser.error, parser.problem, parser.problem_mark.line + 1, parser.problem_mark.column + 1);
            ret = -4;
            break;
        }

        switch (event.type) {
        case YAML_MAPPING_START_EVENT: handle_mapping_start(&state); break;
        case YAML_MAPPING_END_EVENT:   handle_mapping_end(&state);   break;
        case YAML_SCALAR_EVENT:        handle_scalar(&state, (const char *)event.data.scalar.value); break;
        case YAML_STREAM_END_EVENT:    done = 1; break;
        default: break;
        }
        yaml_event_delete(&event);
    }

    yaml_parser_delete(&parser);
    fclose(fp);
    return ret;
}
