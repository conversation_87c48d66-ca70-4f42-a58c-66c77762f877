#include <an_calc.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <an_log.h>

#define LOG_TAG "oper_ip_abroad"

// --- 1. 为逻辑名定义常量 ---
static const char *const LOGICAL_NAME_SIP_ABROAD = "sip_abroad";
static const char *const LOGICAL_NAME_DIP_ABROAD = "dip_abroad";

// --- 算子私有上下文 ---
typedef struct {
    int sip_abroad_index;
    int dip_abroad_index;
} ip_abroad_ctx_t;


// --- 2. 实现自我描述接口 ---

// 声明本算子依赖两个 double 类型的变量
static const an_operator_variable_req_t ip_abroad_dependencies[] = {
    {LOGICAL_NAME_SIP_ABROAD, AN_VAR_TYPE_DOUBLE},
    {LOGICAL_NAME_DIP_ABROAD, AN_VAR_TYPE_DOUBLE},
    {NULL, AN_VAR_TYPE_UNKNOWN} // 数组结束标记
};

// 返回依赖声明
const an_operator_variable_req_t *get_deps()
{
    return ip_abroad_dependencies;
}

// 本算子没有“副产品”输出（即除了主 double 返回值之外的输出），所以返回 NULL
const an_operator_variable_req_t *get_outs()
{
    return NULL;
}


// --- 算子生命周期和执行函数 ---

double ip_abroad_execute(an_calc_session_t *session, an_calc_oper_ctx_t *oper_ctx)
{
    ip_abroad_ctx_t *ctx = (ip_abroad_ctx_t *)oper_ctx->user;
    if (!ctx) {
        return 0.0;
    }

    // 如果某个依赖在 init 时未解析成功，其 index 会是 -1
    // an_calc_get_double 对无效 index 会返回 0.0，行为是安全的
    double sip_abroad = an_calc_get_double(session, ctx->sip_abroad_index);
    double dip_abroad = an_calc_get_double(session, ctx->dip_abroad_index);

    if (sip_abroad > 0.0 || dip_abroad > 0.0) {
        return 1.0; // HIT
    }

    return 0.0;
}

// --- 3. 重构 init 函数，使用框架注入的接口 ---
int ip_abroad_init(an_calc_oper_ctx_t *oper_ctx)
{
    LOG_INFO(LOG_TAG, "Initializing op instance: oper_ip_abroad");

    ip_abroad_ctx_t *ctx = (ip_abroad_ctx_t *)calloc(1, sizeof(ip_abroad_ctx_t));
    if (!ctx) {
        return -1;
    }

    // 通过框架注入的函数获取依赖索引，代码更简洁、更安全
    ctx->sip_abroad_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_SIP_ABROAD);
    ctx->dip_abroad_index = oper_ctx->get_depend_index(oper_ctx, LOGICAL_NAME_DIP_ABROAD);

    // 检查依赖是否成功解析
    if (ctx->sip_abroad_index < 0) {
        LOG_ERROR(LOG_TAG, "Failed to resolve dependency '%s'. This operator may not function correctly.", LOGICAL_NAME_SIP_ABROAD);
        // 根据业务需求，这里可以选择是否要中断初始化
        // free(ctx);
        // return -1;
    }

    if (ctx->dip_abroad_index < 0) {
        LOG_ERROR(LOG_TAG, "Failed to resolve dependency '%s'. This operator may not function correctly.", LOGICAL_NAME_DIP_ABROAD);
    }

    oper_ctx->user = ctx;
    LOG_INFO(LOG_TAG, "Dependencies resolved: %s -> index %d, %s -> index %d",
             LOGICAL_NAME_SIP_ABROAD, ctx->sip_abroad_index,
             LOGICAL_NAME_DIP_ABROAD, ctx->dip_abroad_index);

    return 0;
}

void ip_abroad_cleanup(an_calc_oper_ctx_t *oper_ctx)
{
    LOG_INFO(LOG_TAG, "Cleaning up op instance: oper_ip_abroad");
    if (oper_ctx && oper_ctx->user) {
        free(oper_ctx->user);
        oper_ctx->user = NULL;
    }
}

// 4. 更新导出的插件结构体
an_operator_plugin_t oper_ip_abroad = {
    .name = "oper_ip_abroad",
    .version = "1.0.0",
    .get_dependencies = get_deps,
    .get_outputs = get_outs, // 明确设置为 get_outs
    .init = ip_abroad_init,
    .fini = ip_abroad_cleanup,
    .execute = ip_abroad_execute,
    .reset = NULL // 这个算子是无状态的，不需要 reset
};
