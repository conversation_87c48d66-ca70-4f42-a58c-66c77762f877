/**
 * @file util_log.h
 * @brief 增强的日志记录与条件处理宏
 *
 * 提供了一套强大的宏，用于在满足特定条件时记录日志并执行相应操作（如跳转、返回或执行代码块）。
 * 这有助于编写更干净、更健壮的错误处理逻辑。
 *
 * 主要特性:
 * - 将条件检查、日志记录和后续动作合并为一行。
 * - 支持 GOTO、RETURN 和自定义代码块三种常用错误处理模式。
 * - 支持 ERROR, WARN, INFO, DEBUG 等不同日志级别。
 * - 采用 `do-while(0)` 封装，确保宏在任何语法环境中都能安全使用。
 */

#ifndef __UTIL_LOG_H__
#define __UTIL_LOG_H__

#include <an_log.h> // 假设这是你的日志库头文件

 /* --- 内部核心宏 (不建议直接使用) --- */

 /**
  * @brief 核心实现宏，当条件为真时记录日志并执行一个动作。
  * @param log_func  日志函数 (例如 LOG_ERROR)
  * @param condition 条件表达式
  * @param action    要执行的完整 C 语句 (例如 `goto cleanup;` 或 `return -1;`)
  * @param tag       日志标签
  * @param fmt       日志格式化字符串
  * @param ...       可变参数
  */
#define _LOG_IF_ACTION(log_func, condition, action, tag, fmt, ...) \
     do {                                                          \
        if (condition) {                                           \
            log_func(tag, fmt, ##__VA_ARGS__);                     \
            action; /* 执行完整的动作语句 */                       \
        }                                                          \
     } while (0)

/* ========================================================================= */
/*       方案 1: GOTO - 用于复杂的资源清理流程                             */
/* ========================================================================= */

/**
 * @brief 若条件为真，记录ERROR日志并跳转到指定标签。
 * @example
 *   CHECK_ERR_GOTO(file == NULL, TAG, cleanup, "Failed to open file: %s", path);
 */
#define CHECK_ERR_GOTO(condition, tag, label, fmt, ...) \
     _LOG_IF_ACTION(LOG_ERROR, condition, goto label, tag, fmt, ##__VA_ARGS__)

#define CHECK_WARN_GOTO(condition, tag, label, fmt, ...) \
     _LOG_IF_ACTION(LOG_WARN, condition, goto label, tag, fmt, ##__VA_ARGS__)

#define CHECK_DEBUG_GOTO(condition, tag, label, fmt, ...) \
     _LOG_IF_ACTION(LOG_DEBUG, condition, goto label, tag, fmt, ##__VA_ARGS__)

#define CHECK_INFO_GOTO(condition, tag, label, fmt, ...) \
     _LOG_IF_ACTION(LOG_INFO, condition, goto label, tag, fmt, ##__VA_ARGS__)


/* ========================================================================= */
/*       方案 2: RETURN - 用于简单的提前退出                                 */
/* ========================================================================= */

/**
* @brief 若条件为真，记录ERROR日志并从当前函数返回一个值。
* @param retval 要返回的值。
* @example
*   int* p = malloc(size);
*   CHECK_ERR_RETURN(p == NULL, TAG, NULL, "Memory allocation failed");
*/
#define CHECK_ERR_RETURN(condition, tag, retval, fmt, ...) \
     _LOG_IF_ACTION(LOG_ERROR, condition, return (retval), tag, fmt, ##__VA_ARGS__)

/**
 * @brief 若条件为真，记录ERROR日志并从 void 函数返回。
 * @example
 *   CHECK_ERR_RETURN_VOID(ptr == NULL, TAG, "Invalid pointer provided");
 */
#define CHECK_ERR_RETURN_VOID(condition, tag, fmt, ...) \
     _LOG_IF_ACTION(LOG_ERROR, condition, return, tag, fmt, ##__VA_ARGS__)


/* ========================================================================= */
/*       方案 3: EXEC - 用于执行自定义单条或多条语句                         */
/* ========================================================================= */

/**
 * @brief 若条件为真，记录ERROR日志并执行一个自定义代码块。
 * @param code_block 要执行的代码。可以是单条语句 (以分号结尾) 或用 `{}` 包围的多条语句。
 *
 * @example (单条语句)
 *   CHECK_ERR_EXEC(size <= 0, TAG, return -1;, "Invalid size: %d", size);
 *
 * @example (多条语句)
 *   CHECK_ERR_EXEC(ctx == NULL, TAG, { free(data); return false; }, "Context is null");
 */
#define CHECK_ERR_EXEC(condition, tag, code_block, fmt, ...) \
     _LOG_IF_ACTION(LOG_ERROR, condition, code_block, tag, fmt, ##__VA_ARGS__)

#define CHECK_WARN_EXEC(condition, tag, code_block, fmt, ...) \
     _LOG_IF_ACTION(LOG_WARN, condition, code_block, tag, fmt, ##__VA_ARGS__)

#define CHECK_DEBUG_EXEC(condition, tag, code_block, fmt, ...) \
     _LOG_IF_ACTION(LOG_DEBUG, condition, code_block, tag, fmt, ##__VA_ARGS__)

#endif /* __UTIL_LOG_H__ */
