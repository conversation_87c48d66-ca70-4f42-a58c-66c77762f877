#include "pipeline.h" // 假设这是您项目的公共头文件
#include <an_pipeline.h>
#include <an_common.h>
#include <an_mpool.h>
#include <an_cpu_cfg.h>
#include <an_config.h>
#include "plugin_api.h"
#include "common.h"
#include <cvector.h>
#include <signal.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <zmq.h>
#include "cmdline.h"
#include "config.h"

// --- 类型定义 ---

typedef void (*plugin_destroyer_func_t)(void *);

// 应用上下文结构体
typedef struct
{
    pipeline_t *pipeline;
    //an_config_node_t *config_root;
    cvector_vector_type(perf_mempool_t *) global_pools;
    cvector_vector_type(void *) plugin_contexts;
    cvector_vector_type(plugin_destroyer_func_t) context_destroyers;
} pipeline_app_context_t;

// --- 全局上下文和函数声明 ---

// 全局上下文指针，主要用于信号处理器
static pipeline_app_context_t *g_app_ctx = NULL;

static int show_pip_mem(int argc, char** argv, char* buf, size_t resp_size, void* user_data)
{
    int len = 0;
    pipeline_app_context_t *app_ctx = (pipeline_app_context_t *)user_data;
    CMD_PRINT("Name              Total     Avail     MAX\n");
    CMD_PRINT("------------------------------------------------\n");

    for (int i = 0; i < cvector_size(app_ctx->global_pools); i++)
    {
        perf_mempool_t *pool = app_ctx->global_pools[i];
        int avail = perf_mempool_avail_count(pool);
        int max = pool->size - perf_mempool_get_min_avail_count(pool);
        CMD_PRINT("%-18s%-10u%-10u%-10u\n",
                    pool->name, pool->size, avail, max);
    }

    //CMD_PRINT("-------------------------------------------------\n");
    return len;
}
/**
 * @brief 从 'global_settings' 节点解析并创建全局资源。
 */
static bool parse_global_resources(pipeline_app_context_t *app_ctx, an_config_node_t *global_node)
{
    const char *pools[] = {"msg_pool", "str_pool"};
    for (size_t i = 0; i < sizeof(pools) / sizeof(pools[0]); ++i)
    {
        an_config_node_t *pool_node = an_config_query(global_node, pools[i]);
        if (!pool_node)
            continue;

        const char *name = an_config_get_string(an_config_query(pool_node, "name"), "");
        int64_t pool_size = an_config_get_int(an_config_query(pool_node, "pool_size"), 0);
        int64_t cache_size = an_config_get_int(an_config_query(pool_node, "cache_size"), 0);
        const char *obj_size_str = an_config_get_string(an_config_query(pool_node, "obj_size"), "0");

        size_t obj_size = 0;
        if (strcmp(obj_size_str, "risk_message_t") == 0)
        {
            obj_size = sizeof(risk_message_t); // 假设 risk_message_t 已定义
        }
        else
        {
            obj_size = atol(obj_size_str);
        }

        if (strlen(name) == 0 || !pool_size || !obj_size || !cache_size)
        {
            LOG_ERROR(LOG_TAG, "Incomplete memory pool configuration for '%s'", name);
            return false;
        }

        perf_mempool_t *pool = perf_mempool_create(name, pool_size, obj_size, cache_size, 0, NULL, NULL);
        if (!pool)
        {
            LOG_ERROR(LOG_TAG, "Failed to create mempool '%s'", name);
            return false;
        }
        cvector_push_back(app_ctx->global_pools, pool);

        LOG_INFO(LOG_TAG, "Creating mempool '%s' pool_size:%d obj_size:%d cache_size:%d", name, pool_size, obj_size, cache_size);
    }
    return true;
}

/**
 * @brief 解析 IO 配置节点 (input_config or output_config)。
 */
static bool parse_io_config(io_config_t *io_cfg, an_config_node_t *io_node)
{
    io_cfg->name = (char *)an_config_get_string(an_config_query(io_node, "name"), "");

    const char *type_str = an_config_get_string(an_config_query(io_node, "type"), "NONE");
    if (strcasecmp(type_str, "ZMQ") == 0)
        io_cfg->type = IO_TYPE_ZMQ;
    else if (strcasecmp(type_str, "QUEUE") == 0)
        io_cfg->type = IO_TYPE_QUEUE;
    else
        io_cfg->type = IO_TYPE_NONE;

    if (io_cfg->type == IO_TYPE_ZMQ)
    {
        io_cfg->zmq.endpoint = (char *)an_config_get_string(an_config_query(io_node, "endpoint"), NULL);
        if (!io_cfg->zmq.endpoint)
        {
            LOG_ERROR(LOG_TAG, "ZMQ I/O config '%s' is missing 'endpoint'.", io_cfg->name);
            return false;
        }

        const char *sock_type_str = an_config_get_string(an_config_query(io_node, "socket_type"), "");
        if (strcasecmp(sock_type_str, "PULL") == 0)
            io_cfg->zmq.socket_type = ZMQ_PULL;
        else if (strcasecmp(sock_type_str, "PUSH") == 0)
            io_cfg->zmq.socket_type = ZMQ_PUSH;
        else if (strcasecmp(sock_type_str, "DEALER") == 0)
            io_cfg->zmq.socket_type = ZMQ_DEALER;
        else if (strcasecmp(sock_type_str, "ROUTER") == 0)
            io_cfg->zmq.socket_type = ZMQ_ROUTER;

        io_cfg->zmq.is_server = an_config_get_bool(an_config_query(io_node, "is_server"), false);
        io_cfg->zmq.use_trans = an_config_get_bool(an_config_query(io_node, "use_trans"), false);
        io_cfg->zmq.buffer_size = an_config_get_int(an_config_query(io_node, "buffer_size"), 0);
    }
    else if (io_cfg->type == IO_TYPE_QUEUE)
    {
        io_cfg->queue.capacity = an_config_get_int(an_config_query(io_node, "capacity"), 1024);
    }
    return true;
}

/**
 * @brief 解析并注册一个 stage 的所有插件。
 */
static bool parse_plugins_for_stage(pipeline_app_context_t *app_ctx, const char *stage_name, an_config_node_t *plugins_node)
{
    if (plugins_node->type != AN_CONFIG_NODE_SEQUENCE)
        return true; // No plugins is not an error

    size_t num_plugins = an_config_sequence_size(plugins_node);
    for (size_t i = 0; i < num_plugins; ++i)
    {
        an_config_node_t *plugin_node = an_config_sequence_get(plugins_node, i);
        const char *plugin_name = an_config_get_string(an_config_query(plugin_node, "name"), NULL);
        if (!plugin_name)
            continue;

        plugin_t plugin = {0};
        void *p_ctx = NULL;
        plugin_destroyer_func_t p_destroyer = NULL;

        an_config_node_t *params_node = an_config_query(plugin_node, "params");

        if (strcmp(plugin_name, "json_parser") == 0)
        {
            p_ctx = plugin_json_parser_ctx_create(app_ctx->global_pools, params_node);
            p_destroyer = (plugin_destroyer_func_t)plugin_json_parser_ctx_destroy;
            if (p_ctx)
                plugin = plugin_json_parser_get(p_ctx);
        }
        else if (strcmp(plugin_name, "workflow_processor") == 0)
        {
            p_ctx = plugin_workflow_ctx_create(app_ctx->global_pools, params_node);
            p_destroyer = (plugin_destroyer_func_t)plugin_workflow_ctx_destroy;
            if (p_ctx)
                plugin = plugin_workflow_get(p_ctx);
        }
        else if (strcmp(plugin_name, "es_writer") == 0)
        {
            p_ctx = plugin_es_writer_ctx_create(app_ctx->global_pools, params_node);
            p_destroyer = (plugin_destroyer_func_t)plugin_es_writer_ctx_destroy;
            if (p_ctx)
                plugin = plugin_es_writer_get(p_ctx);
        }
        else
        {
            LOG_WARN(LOG_TAG, "Unknown plugin type '%s' in stage '%s'. Skipping.", plugin_name, stage_name);
            continue;
        }

        if (p_ctx)
        {
            cvector_push_back(app_ctx->plugin_contexts, p_ctx);
            cvector_push_back(app_ctx->context_destroyers, p_destroyer);
            if (pipeline_register_plugin(app_ctx->pipeline, stage_name, &plugin) != PIPELINE_SUCCESS)
            {
                LOG_ERROR(LOG_TAG, "Failed to register plugin '%s' to stage '%s'", plugin_name, stage_name);
                return false;
            }
        }
        else
        {
            LOG_ERROR(LOG_TAG, "Failed to create context for plugin '%s'", plugin_name);
            return false;
        }
    }
    return true;
}

/**
 * @brief 解析单个 stage 节点并将其注册到 pipeline。
 */
static bool parse_single_stage(pipeline_app_context_t *app_ctx, an_config_node_t *stage_node)
{
    stage_config_t stage_cfg = {0};

    stage_cfg.name = (char *)an_config_get_string(an_config_query(stage_node, "name"), NULL);
    if (!stage_cfg.name)
    {
        LOG_ERROR(LOG_TAG, "A stage in the configuration is missing a 'name'.");
        return false;
    }

    stage_cfg.thread_count = an_config_get_int(an_config_query(stage_node, "thread_count"), 1);
    stage_cfg.sleep_ms = an_config_get_int(an_config_query(stage_node, "sleep_ms"), 0);
    stage_cfg.use_prev_output = an_config_get_bool(an_config_query(stage_node, "use_prev_output"), false);

    // 解析 IO 配置
    an_config_node_t *input_node = an_config_query(stage_node, "input_config");
    if (input_node && !parse_io_config(&stage_cfg.input_config, input_node))
        return false;

    an_config_node_t *output_node = an_config_query(stage_node, "output_config");
    if (output_node && !parse_io_config(&stage_cfg.output_config, output_node))
        return false;

    // 注册 Stage
    if (pipeline_register_stage(app_ctx->pipeline, &stage_cfg) != PIPELINE_SUCCESS)
    {
        LOG_ERROR(LOG_TAG, "Failed to register stage: %s", stage_cfg.name);
        return false;
    }

    // 解析并注册该 Stage 的插件
    an_config_node_t *plugins_node = an_config_query(stage_node, "plugins");
    if (plugins_node && !parse_plugins_for_stage(app_ctx, stage_cfg.name, plugins_node))
    {
        return false;
    }

    return true;
}

/**
 * @brief 遍历 'stages' 序列节点，解析并注册每一个 stage。
 */
static bool parse_stages(pipeline_app_context_t *app_ctx, an_config_node_t *stages_node)
{
    if (stages_node->type != AN_CONFIG_NODE_SEQUENCE)
    {
        LOG_ERROR(LOG_TAG, "'stages' is not a sequence.");
        return false;
    }

    size_t num_stages = an_config_sequence_size(stages_node);
    for (size_t i = 0; i < num_stages; ++i)
    {
        an_config_node_t *stage_node = an_config_sequence_get(stages_node, i);
        if (!parse_single_stage(app_ctx, stage_node))
        {
            return false;
        }
    }
    return true;
}

/**
 * @brief 负责从配置构建和启动流水线的核心逻辑。
 */
static bool build_pipeline_from_config(pipeline_app_context_t *app_ctx)
{
    an_config_node_t *config_root = get_app_conf();
    if (!config_root)
    {
        return false;
    }

    // 2. 解析全局资源
    an_config_node_t *global_node = an_config_query(config_root, "global_settings");
    if (global_node && !parse_global_resources(app_ctx, global_node))
    {
        return false;
    }

    // 3. 创建 Pipeline
    app_ctx->pipeline = pipeline_create();
    if (!app_ctx->pipeline)
    {
        return false;
    }

    // 4. 解析并注册 Stages
    an_config_node_t *stages_node = an_config_query(config_root, "stages");
    if (stages_node && !parse_stages(app_ctx, stages_node))
    {
        return false;
    }
    //注册命令行
    an_cmd_reg_register(get_cmd_reg(), "show_pip_mem", "show pipeline", show_pip_mem, app_ctx);
    an_cmd_reg_register(get_cmd_reg(), "show_pipeline_stat", "show pipeline stat", (command_handler_t)show_pipeline_stat, app_ctx->pipeline);

    // 5. 启动 Pipeline
    if (pipeline_start(app_ctx->pipeline) != PIPELINE_SUCCESS)
    {
        LOG_ERROR(LOG_TAG, "Failed to start pipeline");
        return false;
    }

    return true;
}

/**
 * @brief 应用程序的主入口点。初始化、构建并启动流水线，然后等待停止信号。
 */
int pipeline_app_start()
{
    // 创建并初始化应用上下文
    g_app_ctx = calloc(1, sizeof(pipeline_app_context_t));
    if (!g_app_ctx)
    {
        LOG_ERROR(LOG_TAG, "Failed to allocate app context.");
        return -1;
    }

    // 从配置文件构建和启动流水线
    if (!build_pipeline_from_config(g_app_ctx))
    {
        LOG_ERROR(LOG_TAG, "Failed to build or start the pipeline.");
        // cleanup_app_context(g_app_ctx);
        // g_app_ctx = NULL;
        return -1;
    }

    LOG_INFO(LOG_TAG, "Pipeline application is running. Press Ctrl+C to exit.");

    return 0;
}

/**
 * @brief 清理应用上下文的所有资源，确保幂等性。
 */
static void cleanup_app_context(pipeline_app_context_t *app_ctx)
{
    if (!app_ctx)
        return;

    // 停止并销毁 pipeline (pipeline_destroy 内部应处理停止逻辑)
    if (app_ctx->pipeline)
    {
        pipeline_destroy(app_ctx->pipeline);
        app_ctx->pipeline = NULL;
    }

    // 销毁插件上下文 (后进先出，虽然通常顺序不重要)
    if (app_ctx->plugin_contexts)
    {
        for (size_t i = cvector_size(app_ctx->plugin_contexts); i > 0; --i)
        {
            if (app_ctx->context_destroyers[i - 1])
            {
                app_ctx->context_destroyers[i - 1](app_ctx->plugin_contexts[i - 1]);
            }
        }
        cvector_free(app_ctx->plugin_contexts);
        cvector_free(app_ctx->context_destroyers);
        app_ctx->plugin_contexts = NULL;
        app_ctx->context_destroyers = NULL;
    }

    // 销毁全局内存池
    if (app_ctx->global_pools)
    {
        for (size_t i = 0; i < cvector_size(app_ctx->global_pools); ++i)
        {
            perf_mempool_destroy(app_ctx->global_pools[i]);
        }
        cvector_free(app_ctx->global_pools);
        app_ctx->global_pools = NULL;
    }

    // 释放应用上下文自身
    free(app_ctx);
}

void pipeline_app_stop()
{
    // 清理资源
    cleanup_app_context(g_app_ctx);
    LOG_INFO(LOG_TAG, "Pipeline application shutdown complete.");
    g_app_ctx = NULL;
}
